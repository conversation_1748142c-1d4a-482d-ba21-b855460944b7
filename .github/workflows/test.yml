name: Test Suite

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ develop ]

jobs:
  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/client/package-lock.json

    - name: Install dependencies
      working-directory: ./src/client
      run: npm ci

    - name: Run ESLint
      working-directory: ./src/client
      run: npm run lint

    - name: Build frontend
      working-directory: ./src/client
      run: npm run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: frontend-build
        path: src/client/dist/
        retention-days: 1

  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: maven
        
    - name: Run tests
      working-directory: ./src/server
      run: ./mvnw test

    - name: Generate test report
      working-directory: ./src/server
      run: ./mvnw surefire-report:report

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: src/server/target/surefire-reports/
        retention-days: 7

    - name: Build JAR
      working-directory: ./src/server
      run: ./mvnw clean package -DskipTests

    - name: Upload JAR artifact
      uses: actions/upload-artifact@v4
      with:
        name: backend-jar
        path: src/server/target/*.jar
        retention-days: 1

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download frontend build
      uses: actions/download-artifact@v4
      with:
        name: frontend-build
        path: src/client/dist/

    - name: Download backend JAR
      uses: actions/download-artifact@v4
      with:
        name: backend-jar
        path: src/server/target/
        
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        load: true
        tags: botc-assistant:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Run integration tests
      run: |
        # Start the container
        docker run -d --name botc-assistant-test -p 8080:8080 botc-assistant:test

        # Wait for application to start
        timeout 60 bash -c 'until curl -f http://localhost:8080/; do sleep 2; done'

        # Test landing page
        echo "Testing landing page..."
        curl -f http://localhost:8080/

        echo "All integration tests passed!"
        
    - name: Cleanup
      if: always()
      run: |
        docker stop botc-assistant-test || true
        docker rm botc-assistant-test || true

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'table'
        exit-code: '0'  # Don't fail the build on vulnerabilities

    # Note: Code scanning upload disabled for private repositories
    # Trivy results are visible in the job output above

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache SonarCloud packages
      uses: actions/cache@v3
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar
        
    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    # Uncomment and configure if you want SonarCloud integration
    # - name: Build and analyze
    #   env:
    #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    #   working-directory: ./server
    #   run: ./mvnw clean verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.projectKey=your-project-key
