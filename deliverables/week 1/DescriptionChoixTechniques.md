# Description des choix techniques

Berberat Alex  
Gorgerat <PERSON>

## Backend
The backend of the project is built using `Spring Boot 3.3.0`, a widely adopted Java framework that makes the development of production-ready applications easier. The choice of `Java 17` ensures the long-term support and the access to some of the latest language features. `<PERSON>ven` is used as dependency management and build tool due to the ease of integration with CI/CD pipelines and some others perks.

## Frontend
For the frontend, the project uses `React 18`, a popular and performant JavaScript library for building user interfaces. The project uses `Vite` as the build tool and development server, chosen for the hot module replacement (HMR) to optimize build times. Styling is implemented with `CSS3`, to allow modern styling with gradients, animations,... and responsive design, while the codebase uses `ES6+` standards to take advantage of modern JavaScript features for a cleaner and more efficient code. 

## Testing
Testing is done with `JUnit 5`, a powerful Java testing framework to do unit testing on the backend.  
`GitHub Actions` is used for continuous integration, running automated tests on each commit to ensure code quality and early detection of issues.

## Deployment
The application is containerized using `Docker`, providing a consistent runtime environment across development, testing and production which simplifies deployment and scaling.  
`GitHub Actions` is also used for continuous deployment workflows, allowing CI/CD pipeline automation to improve delivery speed and reliability.

