package com.botc.assistant.roles;

/**
 * Abstract base class for all Blood on the Clock Tower roles.
 * Provides common implementation for role properties.
 */
public abstract class AbstractRole implements Role {
    
    protected final String id;
    protected final String name;
    protected final String description;
    protected final RoleTeam team;
    protected final RoleType type;
    protected final boolean isUnique;
    protected final boolean hasNightAbility;
    protected final boolean hasDayAbility;
    protected final Integer nightPriority;
    protected final boolean canBeTargeted;
    protected final String setupInfo;
    
    protected AbstractRole(Builder<?> builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.description = builder.description;
        this.team = builder.team;
        this.type = builder.type;
        this.isUnique = builder.isUnique;
        this.hasNightAbility = builder.hasNightAbility;
        this.hasDayAbility = builder.hasDayAbility;
        this.nightPriority = builder.nightPriority;
        this.canBeTargeted = builder.canBeTargeted;
        this.setupInfo = builder.setupInfo;
    }
    
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getDescription() {
        return description;
    }
    
    @Override
    public RoleTeam getTeam() {
        return team;
    }
    
    @Override
    public RoleType getType() {
        return type;
    }
    
    @Override
    public boolean isUnique() {
        return isUnique;
    }
    
    @Override
    public boolean hasNightAbility() {
        return hasNightAbility;
    }
    
    @Override
    public boolean hasDayAbility() {
        return hasDayAbility;
    }
    
    @Override
    public Integer getNightPriority() {
        return nightPriority;
    }
    
    @Override
    public boolean canBeTargeted() {
        return canBeTargeted;
    }
    
    @Override
    public String getSetupInfo() {
        return setupInfo;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AbstractRole that = (AbstractRole) obj;
        return id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s)", name, type.getDisplayName());
    }
    
    /**
     * Builder pattern for creating roles
     */
    public abstract static class Builder<T extends Builder<T>> {
        protected String id;
        protected String name;
        protected String description;
        protected RoleTeam team;
        protected RoleType type;
        protected boolean isUnique = true;
        protected boolean hasNightAbility = false;
        protected boolean hasDayAbility = false;
        protected Integer nightPriority = null;
        protected boolean canBeTargeted = true;
        protected String setupInfo = null;
        
        public T id(String id) {
            this.id = id;
            return self();
        }
        
        public T name(String name) {
            this.name = name;
            return self();
        }
        
        public T description(String description) {
            this.description = description;
            return self();
        }
        
        public T team(RoleTeam team) {
            this.team = team;
            return self();
        }
        
        public T type(RoleType type) {
            this.type = type;
            return self();
        }
        
        public T isUnique(boolean isUnique) {
            this.isUnique = isUnique;
            return self();
        }
        
        public T hasNightAbility(boolean hasNightAbility) {
            this.hasNightAbility = hasNightAbility;
            return self();
        }
        
        public T hasDayAbility(boolean hasDayAbility) {
            this.hasDayAbility = hasDayAbility;
            return self();
        }
        
        public T nightPriority(Integer nightPriority) {
            this.nightPriority = nightPriority;
            return self();
        }
        
        public T canBeTargeted(boolean canBeTargeted) {
            this.canBeTargeted = canBeTargeted;
            return self();
        }
        
        public T setupInfo(String setupInfo) {
            this.setupInfo = setupInfo;
            return self();
        }
        
        protected abstract T self();
        
        public abstract AbstractRole build();
    }
}
