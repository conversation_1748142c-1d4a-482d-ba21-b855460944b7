# Authentication-Aware Navigation Features

This document describes the authentication-aware navigation features implemented in the NavBar component.

## Features Implemented

### 1. **Authentication-Aware Login/Logout Button**
- **When NOT logged in**: Shows "Se connecter" button that links to `/login`
- **When logged in**: Shows "Se déconnecter" button that calls the logout function
- **Loading state**: Shows "Déconnexion..." during logout process
- **Auto-redirect**: After logout, user is redirected to home page

### 2. **User Status Indicator**
- **When logged in**: Displays a user badge with the format "👤 [DisplayName]"
- **Styling**: Styled as a subtle badge with background and border
- **Position**: Appears at the top of the mobile menu, near logout button on desktop
- **Responsive**: Different styling for mobile vs desktop views

### 3. **Play Button for Authenticated Users**
- **When logged in**: Shows "Jouer" button that navigates to `/play` route
- **When NOT logged in**: Play button is hidden
- **Current behavior**: Redirects to home page (since `/play` page doesn't exist yet)
- **Styling**: Green accent border to distinguish it as a primary action

### 4. **Reactive UI Updates**
- **Real-time updates**: Navigation updates immediately when authentication state changes
- **Context integration**: Uses `useAuth` hook from AuthContext
- **Loading states**: Handles loading states during authentication checks

## Component Structure

### NavBar.jsx
- **Purpose**: Main navigation wrapper
- **Authentication integration**: Passes auth state to MenuBurger component
- **Props passed**: `status` (isAuthenticated), `mj` (isNarrator), `loading`

### Burger.jsx (MenuBurger)
- **Purpose**: Handles the actual menu rendering and interactions
- **Authentication features**:
  - User status indicator
  - Login/logout button switching
  - Play button visibility
  - Logout functionality with error handling

## Styling

### Mobile View
- User badge appears as a separate section at top of menu
- Buttons stack vertically in the dropdown menu
- Play button has left green border accent

### Desktop View
- User badge appears as a rounded pill next to other nav items
- All items display horizontally
- Play button has right green border accent
- Consistent spacing and hover effects

## Usage Examples

### For Unauthenticated Users
```
Navigation shows:
- Logo (links to home)
- "Se connecter" button
- "Règles" link
- "Rôles" link
```

### For Authenticated Players
```
Navigation shows:
- Logo (links to home)
- User badge: "👤 PlayerName"
- "Jouer" button (green accent)
- "Se déconnecter" button
- "Règles" link
- "Rôles" link
- Player-specific links (Table, Partie)
```

### For Authenticated Narrators
```
Navigation shows:
- Logo (links to home)
- User badge: "👤 NarratorName"
- "Jouer" button (green accent)
- "Se déconnecter" button
- "Règles" link
- "Rôles" link
- Narrator-specific links (Table MJ, Tour)
```

## Future Enhancements

1. **Play Page**: When `/play` page is implemented, update the redirect behavior
2. **User Profile**: Add link to user profile/settings page
3. **Notifications**: Add notification badge for game invites or updates
4. **Quick Actions**: Add dropdown for quick game actions

## Technical Notes

- Uses React hooks for state management
- Integrates with Firebase Authentication via AuthContext
- Responsive design with mobile-first approach
- Accessible button implementations
- Error handling for logout operations
