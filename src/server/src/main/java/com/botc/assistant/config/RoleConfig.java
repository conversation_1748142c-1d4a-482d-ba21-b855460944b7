package com.botc.assistant.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Configuration properties for role-related settings.
 * Allows customization of role behavior through application properties.
 */
@Configuration
@ConfigurationProperties(prefix = "botc.roles")
public class RoleConfig {
    
    /**
     * Whether to enable role validation during game setup
     */
    private boolean enableValidation = true;
    
    /**
     * Whether to allow custom role distributions
     */
    private boolean allowCustomDistribution = false;
    
    /**
     * Default roles to include in new games
     */
    private List<String> defaultRoles = List.of(
        "chef", "investigator", "librarian", "empath", "monk", "ravenkeeper"
    );
    
    /**
     * Maximum number of players allowed in a game
     */
    private int maxPlayers = 20;
    
    /**
     * Minimum number of players required for a game
     */
    private int minPlayers = 5;
    
    /**
     * Whether to automatically assign roles during setup
     */
    private boolean autoAssignRoles = false;
    
    /**
     * Whether to shuffle role assignments
     */
    private boolean shuffleAssignments = true;
    
    // Getters and Setters
    public boolean isEnableValidation() {
        return enableValidation;
    }
    
    public void setEnableValidation(boolean enableValidation) {
        this.enableValidation = enableValidation;
    }
    
    public boolean isAllowCustomDistribution() {
        return allowCustomDistribution;
    }
    
    public void setAllowCustomDistribution(boolean allowCustomDistribution) {
        this.allowCustomDistribution = allowCustomDistribution;
    }
    
    public List<String> getDefaultRoles() {
        return defaultRoles;
    }
    
    public void setDefaultRoles(List<String> defaultRoles) {
        this.defaultRoles = defaultRoles;
    }
    
    public int getMaxPlayers() {
        return maxPlayers;
    }
    
    public void setMaxPlayers(int maxPlayers) {
        this.maxPlayers = maxPlayers;
    }
    
    public int getMinPlayers() {
        return minPlayers;
    }
    
    public void setMinPlayers(int minPlayers) {
        this.minPlayers = minPlayers;
    }
    
    public boolean isAutoAssignRoles() {
        return autoAssignRoles;
    }
    
    public void setAutoAssignRoles(boolean autoAssignRoles) {
        this.autoAssignRoles = autoAssignRoles;
    }
    
    public boolean isShuffleAssignments() {
        return shuffleAssignments;
    }
    
    public void setShuffleAssignments(boolean shuffleAssignments) {
        this.shuffleAssignments = shuffleAssignments;
    }
}
