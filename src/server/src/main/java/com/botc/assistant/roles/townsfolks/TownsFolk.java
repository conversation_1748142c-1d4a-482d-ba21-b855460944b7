package com.botc.assistant.roles.townsfolks;

import com.botc.assistant.roles.AbstractRole;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

/**
 * Abstract base class for all Townsfolk roles.
 * Townsfolk are good team members with helpful abilities.
 */
public abstract class TownsFolk extends AbstractRole {
    
    protected TownsFolk(Builder<?> builder) {
        super(builder);
    }
    
    /**
     * Builder for TownsFolk roles
     */
    public abstract static class Builder<T extends Builder<T>> extends AbstractRole.Builder<T> {
        
        public Builder() {
            // Set default values for all Townsfolk
            team(RoleTeam.GOOD);
            type(RoleType.TOWNSFOLK);
            canBeTargeted(true);
        }
        
        @Override
        public abstract TownsFolk build();
    }
}
