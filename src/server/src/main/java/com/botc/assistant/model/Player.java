package com.botc.assistant.model;

import com.botc.assistant.roles.Role;

import java.util.Objects;
import java.util.UUID;

/**
 * Represents a player in a Blood on the Clock Tower game.
 * Contains player state, voting information, and role assignment.
 */
public class Player {
    
    private final String id;
    private String name;
    private boolean isAlive;
    private boolean lastVote; // true if voted yes in last vote, false if voted no
    private boolean canVote;
    private int position; // Seating position in the circle (0-based)
    private Role role;
    private String notes; // Storyteller or player notes
    private boolean isNominated; // Currently nominated for execution
    private Player votedTarget; // Who this player voted for in current vote
    
    // Constructor
    public Player(String name, int position) {
        this.id = UUID.randomUUID().toString();
        this.name = name;
        this.position = position;
        this.isAlive = true;
        this.canVote = true;
        this.lastVote = false;
        this.isNominated = false;
        this.notes = "";
    }
    
    // Constructor with ID (for testing or specific cases)
    public Player(String id, String name, int position) {
        this.id = id;
        this.name = name;
        this.position = position;
        this.isAlive = true;
        this.canVote = true;
        this.lastVote = false;
        this.isNominated = false;
        this.notes = "";
    }
    
    /**
     * Player votes for a target player.
     * @param target the player being voted for
     * @return true if vote was successful, false otherwise
     */
    public boolean vote(Player target) {
        if (!canVote || !isAlive || target == null) {
            return false;
        }
        
        this.votedTarget = target;
        this.lastVote = true; // Voting for someone counts as a "yes" vote
        return true;
    }
    
    /**
     * Player abstains from voting or votes no.
     * @return true if abstention was successful
     */
    public boolean abstain() {
        if (!canVote || !isAlive) {
            return false;
        }
        
        this.votedTarget = null;
        this.lastVote = false;
        return true;
    }
    
    /**
     * Player uses their role ability.
     * This is a placeholder method that would be implemented based on specific role logic.
     * @return true if ability was used successfully
     */
    public boolean useAbility() {
        if (!isAlive || role == null) {
            return false;
        }
        
        // Role-specific ability logic would be implemented here
        // For now, this is a placeholder
        return role.hasNightAbility() || role.hasDayAbility();
    }
    
    /**
     * Player accuses someone (nominates them for execution).
     * @param target the player being accused/nominated
     * @return true if accusation was successful
     */
    public boolean accuseSomeone(Player target) {
        if (!isAlive || target == null || !target.isAlive || target.isNominated) {
            return false;
        }
        
        target.setNominated(true);
        return true;
    }
    
    /**
     * Kills this player (sets them as dead).
     */
    public void die() {
        this.isAlive = false;
        this.canVote = false; // Dead players typically can't vote
        this.isNominated = false; // Dead players can't be nominated
    }
    
    /**
     * Resurrects this player (sets them as alive).
     * Some roles or storyteller actions can bring players back.
     */
    public void resurrect() {
        this.isAlive = true;
        this.canVote = true;
        this.isNominated = false;
    }
    
    /**
     * Checks if this player is a neighbor of another player.
     * @param other the other player
     * @param totalPlayers total number of players in the game
     * @return true if they are neighbors (sitting next to each other)
     */
    public boolean isNeighborOf(Player other, int totalPlayers) {
        if (other == null || totalPlayers <= 0) {
            return false;
        }
        
        int leftNeighbor = (this.position - 1 + totalPlayers) % totalPlayers;
        int rightNeighbor = (this.position + 1) % totalPlayers;
        
        return other.position == leftNeighbor || other.position == rightNeighbor;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public boolean isAlive() {
        return isAlive;
    }
    
    public void setAlive(boolean alive) {
        isAlive = alive;
        if (!alive) {
            canVote = false;
            isNominated = false;
        }
    }
    
    public boolean isLastVote() {
        return lastVote;
    }
    
    public void setLastVote(boolean lastVote) {
        this.lastVote = lastVote;
    }
    
    public boolean canVote() {
        return canVote;
    }
    
    public void setCanVote(boolean canVote) {
        this.canVote = canVote;
    }
    
    public int getPosition() {
        return position;
    }
    
    public void setPosition(int position) {
        this.position = position;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes != null ? notes : "";
    }
    
    public boolean isNominated() {
        return isNominated;
    }
    
    public void setNominated(boolean nominated) {
        if (!isAlive) {
            this.isNominated = false; // Dead players can't be nominated
        } else {
            this.isNominated = nominated;
        }
    }
    
    public Player getVotedTarget() {
        return votedTarget;
    }
    
    public void setVotedTarget(Player votedTarget) {
        this.votedTarget = votedTarget;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Player player = (Player) obj;
        return Objects.equals(id, player.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return String.format("Player{id='%s', name='%s', position=%d, alive=%s, role='%s'}", 
                           id, name, position, isAlive, role != null ? role.getName() : "none");
    }
}
