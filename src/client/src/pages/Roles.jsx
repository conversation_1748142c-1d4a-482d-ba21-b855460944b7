import Footer from "../components/Footer"
import NavBar from "../components/NavBar"


export default function Roles() {
    return (
        <>
            <NavBar />
            <main id="roles">
                <section id="gameplay">


                    <div>
                        <h1>Gameplay</h1>
                    </div>
                    <div className="text">
                        <p>
                            Lorsque vous jouez à Trouble Brewing...
                            <br />
                            <br />
                            Les bons joueurs devront déterminer qui est bon et qui est mauvais en utilisant leur logique et leur intuition. Certains joueurs peuvent vouloir révéler leur personnage et partager immédiatement leurs informations (comme le Chef ou l'Enquêteur), tandis que d'autres peuvent vouloir mentir sur leur identité afin que l'Imp évite de les attaquer (comme le Croque-mort ou la Voyante). Certains peuvent mentir sur leur identité afin que l'Imp les attaque (comme le Gardien des corbeaux ou le Soldat) !
                            <br />
                            <br />
                            D'autres personnages bons obtiennent des informations en agissant et en observant les effets de leurs actions. Sacrifier sa vie en désignant une Vierge, tenter de tuer l'Imp en tant que Tueur et observer ce qui se passe, ou tuer délibérément des joueurs bons afin que le Croque-mort puisse confirmer leur identité : autant de moyens de sacrifier sa vie et son pouvoir pour obtenir des informations et remporter la victoire.
                            <br />
                            <br />
                            Les joueurs maléfiques devront se faire passer pour des personnages bons et le faire bien, en donnant de fausses informations pour semer la confusion dans l'équipe des bons si nécessaire. Avec uniquement des informations véridiques, l'équipe des bons découvrira généralement qui est maléfique avec suffisamment de temps devant elle. Mais avec ne serait-ce qu'un petit mensonge crédible dans l'air, le mal a une chance. Le Poisoner et le Spy, s'ils font attention, peuvent semer une grande confusion dans les rangs de l'équipe des bons en utilisant leurs capacités de manière sournoise. Les méchants devront également décider quand faire des sacrifices. La Scarlet Woman tuera-t-elle l'Imp pour sauver son équipe ? L'Imp va-t-il se suicider pour transformer un joueur plus fiable en Démon ? L'équipe maléfique votera-t-elle pour exécuter un Minion suspect afin de paraître comme des membres honorables de la ville ? Ou bien le Baron donnera-t-il des informations manifestement fausses pour faire croire qu'un empoisonneur est dans le jeu ? Semer la confusion sur les Minions en jeu peut faire la différence entre la victoire et la défaite.
                        </p>
                    </div>
                </section>


                <section id="townsfolk">
                    <div>
                        <h1>Townsfolk</h1>
                    </div>

                    <div className="role">
                        <div className="title">
                            <img src="/src/assets/Icon_washerwoman.png" alt="icon_washerwoman" />
                            <h2>Washerwoman</h2>
                        </div>


                        <div className="text">
                            <p>
                                « Vous commencez en sachant qu'un des deux joueurs est un villageois particulier. »
                                <br />
                                <br />
                                La lavandière apprend qu'un villageois particulier est en jeu, mais elle ne sait pas qui l'incarne.
                                <br />
                            
                                <ul>
                                    <li>Au cours de la première nuit, la lavandière est réveillée, on lui montre deux joueurs et elle apprend le personnage de l'un d'entre eux.</li>
                                    <li>Elle n'apprend cela qu'une seule fois, puis n'apprend plus rien d'autre.</li>
                                </ul>

                            </p>
                        </div>
                    </div>
                </section>
            </main>
            <Footer />
        </>
    );
}