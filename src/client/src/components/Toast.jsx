// Simple toast notification component
import { useState, useEffect } from 'react';

export default function Toast({ message, type = 'info', duration = 3000, onClose }) {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(false);
            if (onClose) {
                setTimeout(onClose, 300); // Wait for fade out animation
            }
        }, duration);

        return () => clearTimeout(timer);
    }, [duration, onClose]);

    if (!isVisible) {
        return null;
    }

    return (
        <div className={`toast toast-${type} ${isVisible ? 'toast-visible' : 'toast-hidden'}`}>
            <div className="toast-content">
                <span className="toast-icon">
                    {type === 'info' && '🔧'}
                    {type === 'success' && '✅'}
                    {type === 'warning' && '⚠️'}
                    {type === 'error' && '❌'}
                </span>
                <span className="toast-message">{message}</span>
            </div>
            <button 
                className="toast-close" 
                onClick={() => {
                    setIsVisible(false);
                    if (onClose) {
                        setTimeout(onClose, 300);
                    }
                }}
            >
                ×
            </button>
        </div>
    );
}
