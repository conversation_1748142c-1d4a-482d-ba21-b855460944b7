package com.botc.assistant.roles.townsfolks;

/**
 * Investigator - Townsfolk role
 * 
 * You start knowing that 1 of 2 players is a particular Minion.
 * 
 * The Investigator learns information about Minions on the first night.
 * They learn that one of two specific players is a particular type of Minion,
 * but they don't know which of the two players it is.
 */
public class Investigator extends TownsFolk {
    
    public static final String ROLE_ID = "investigator";
    public static final String ROLE_NAME = "Investigator";
    public static final String ROLE_DESCRIPTION = "You start knowing that 1 of 2 players is a particular Minion.";
    
    private Investigator(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Investigator create() {
        return builder().build();
    }
    
    /**
     * Builder for Investigator role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(2); // First night information
            setupInfo("Learns that 1 of 2 players is a particular Minion");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Investigator build() {
            return new Investigator(this);
        }
    }
}
