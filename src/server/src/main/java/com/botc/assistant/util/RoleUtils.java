package com.botc.assistant.util;

import com.botc.assistant.roles.Role;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Utility class for role-related operations.
 * Provides helper methods for role calculations and game setup.
 */
public class RoleUtils {

    private RoleUtils() {
        // Utility class - prevent instantiation
    }

    /**
     * Calculates the recommended role distribution for a given player count.
     * Based on standard Blood on the Clock Tower setup rules.
     * 
     * @param playerCount the number of players
     * @return map of role type to count
     */
    public static Map<RoleType, Integer> calculateRoleDistribution(int playerCount) {
        if (playerCount < 5 || playerCount > 20) {
            throw new IllegalArgumentException("Player count must be between 5 and 20");
        }

        // Standard distribution rules
        int outsiderCount = getOutsiderCount(playerCount);
        int minionCount = getMinionCount(playerCount);
        int demonCount = 1; // Always 1 demon
        int townsfolkCount = playerCount - outsiderCount - minionCount - demonCount;

        return Map.of(
                RoleType.TOWNSFOLK, townsfolkCount,
                RoleType.OUTSIDER, outsiderCount,
                RoleType.MINION, minionCount,
                RoleType.DEMON, demonCount);
    }

    /**
     * Gets the number of outsiders for a given player count.
     * 
     * @param playerCount the number of players
     * @return the number of outsiders
     */
    private static int getOutsiderCount(int playerCount) {
        if (playerCount <= 6)
            return 0;
        if (playerCount <= 9)
            return 1;
        if (playerCount <= 12)
            return 2;
        return 3;
    }

    /**
     * Gets the number of minions for a given player count.
     * 
     * @param playerCount the number of players
     * @return the number of minions
     */
    private static int getMinionCount(int playerCount) {
        if (playerCount <= 6)
            return 1;
        if (playerCount <= 9)
            return 1;
        if (playerCount <= 12)
            return 2;
        if (playerCount <= 15)
            return 3;
        return 4;
    }

    /**
     * Filters roles by team.
     * 
     * @param roles the list of roles to filter
     * @param team  the team to filter by
     * @return filtered list of roles
     */
    public static List<Role> filterByTeam(List<Role> roles, RoleTeam team) {
        return roles.stream()
                .filter(role -> role.getTeam() == team)
                .collect(Collectors.toList());
    }

    /**
     * Filters roles by type.
     * 
     * @param roles the list of roles to filter
     * @param type  the type to filter by
     * @return filtered list of roles
     */
    public static List<Role> filterByType(List<Role> roles, RoleType type) {
        return roles.stream()
                .filter(role -> role.getType() == type)
                .collect(Collectors.toList());
    }

    /**
     * Gets roles with night abilities sorted by priority.
     * 
     * @param roles the list of roles
     * @return sorted list of roles with night abilities
     */
    public static List<Role> getNightActionOrder(List<Role> roles) {
        return roles.stream()
                .filter(Role::hasNightAbility)
                .filter(role -> role.getNightPriority() != null)
                .sorted((r1, r2) -> Integer.compare(r1.getNightPriority(), r2.getNightPriority()))
                .collect(Collectors.toList());
    }

    /**
     * Checks if a role setup is valid for the given player count.
     * 
     * @param roles       the selected roles
     * @param playerCount the number of players
     * @return true if the setup is valid, false otherwise
     */
    public static boolean isValidSetup(List<Role> roles, int playerCount) {
        if (roles.size() != playerCount) {
            return false;
        }

        Map<RoleType, Integer> actualDistribution = roles.stream()
                .collect(Collectors.groupingBy(Role::getType,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)));

        Map<RoleType, Integer> expectedDistribution = calculateRoleDistribution(playerCount);

        return actualDistribution.equals(expectedDistribution);
    }

    /**
     * Counts roles by type in a list.
     * 
     * @param roles the list of roles
     * @return map of role type to count
     */
    public static Map<RoleType, Long> countByType(List<Role> roles) {
        return roles.stream()
                .collect(Collectors.groupingBy(Role::getType, Collectors.counting()));
    }

    /**
     * Counts roles by team in a list.
     * 
     * @param roles the list of roles
     * @return map of role team to count
     */
    public static Map<RoleTeam, Long> countByTeam(List<Role> roles) {
        return roles.stream()
                .collect(Collectors.groupingBy(Role::getTeam, Collectors.counting()));
    }

    /**
     * Checks if a player count is valid for Blood on the Clock Tower.
     * 
     * @param playerCount the number of players
     * @return true if valid, false otherwise
     */
    public static boolean isValidPlayerCount(int playerCount) {
        return playerCount >= 5 && playerCount <= 20;
    }

    /**
     * Gets the minimum number of players needed for a game.
     * 
     * @return minimum player count
     */
    public static int getMinPlayerCount() {
        return 5;
    }

    /**
     * Gets the maximum number of players supported.
     * 
     * @return maximum player count
     */
    public static int getMaxPlayerCount() {
        return 20;
    }
}
