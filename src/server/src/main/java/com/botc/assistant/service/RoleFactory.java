package com.botc.assistant.service;

import com.botc.assistant.roles.Role;
import com.botc.assistant.roles.townsfolks.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Factory for creating and managing role instances.
 * Provides centralized role creation and registration.
 */
@Component
public class RoleFactory {
    
    private final Map<String, Supplier<Role>> roleCreators;
    
    public RoleFactory() {
        this.roleCreators = initializeRoleCreators();
    }
    
    /**
     * Creates a role instance by ID.
     * @param roleId the role ID
     * @return the role instance, or null if not found
     */
    public Role createRole(String roleId) {
        Supplier<Role> creator = roleCreators.get(roleId);
        return creator != null ? creator.get() : null;
    }
    
    /**
     * Gets all available role IDs.
     * @return list of all role IDs
     */
    public List<String> getAllRoleIds() {
        return new ArrayList<>(roleCreators.keySet());
    }
    
    /**
     * Gets all available roles.
     * @return list of all role instances
     */
    public List<Role> getAllRoles() {
        return roleCreators.values().stream()
                .map(Supplier::get)
                .collect(Collectors.toList());
    }
    
    /**
     * Checks if a role ID is valid.
     * @param roleId the role ID to check
     * @return true if the role exists, false otherwise
     */
    public boolean isValidRoleId(String roleId) {
        return roleCreators.containsKey(roleId);
    }
    
    /**
     * Gets the total number of available roles.
     * @return the count of available roles
     */
    public int getRoleCount() {
        return roleCreators.size();
    }
    
    /**
     * Initializes the role creators map with all available roles.
     * @return map of role ID to role creator functions
     */
    private Map<String, Supplier<Role>> initializeRoleCreators() {
        return Map.of(
            // Townsfolk roles
            Chef.ROLE_ID, Chef::create,
            Investigator.ROLE_ID, Investigator::create,
            Librarian.ROLE_ID, Librarian::create,
            Empath.ROLE_ID, Empath::create,
            Monk.ROLE_ID, Monk::create,
            Ravenkeeper.ROLE_ID, Ravenkeeper::create
            
            // TODO: Add other role types (Outsiders, Minions, Demons) as they are implemented
        );
    }
}
