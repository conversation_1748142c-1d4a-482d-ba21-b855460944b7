package com.botc.assistant.roles.townsfolks;

/**
 * <PERSON> - <PERSON>folk role
 * 
 * Each night*, choose a player (not yourself): they are safe from the Demon tonight.
 * 
 * The Monk can protect a player from the Demon each night (except the first night).
 * The protected player cannot be killed by the Demon that night, but they can
 * still be affected by other abilities.
 */
public class Monk extends TownsFolk {
    
    public static final String ROLE_ID = "monk";
    public static final String ROLE_NAME = "Monk";
    public static final String ROLE_DESCRIPTION = "Each night*, choose a player (not yourself): they are safe from the Demon tonight.";
    
    private Monk(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Monk create() {
        return builder().build();
    }
    
    /**
     * Builder for Monk role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(5); // Protection happens before demon kills
            setupInfo("Protects a player from the Demon each night (except first night)");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Monk build() {
            return new Monk(this);
        }
    }
}
