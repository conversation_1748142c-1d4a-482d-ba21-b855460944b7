# Description du processus de travail

Berberat Alex  
Gorgerat Lisa  
<PERSON>

## Outils

Nous avons choisi d’utiliser GitHub comme plateforme centrale pour le développement collaboratif de l’application.

Les principales fonctionnalités exploitées sont :

- Gestion du code source : chaque fonctionnalité est développée sur une branche dédiée, puis intégrée via des pull requests.

- Suivi des tâches : toutes les tâches sont formalisées sous forme d’issues.

- Gestion des versions : utilisation des tags et des releases pour identifier les jalons du projet.

Afin d’organiser efficacement notre travail, nous avons mis en place un tableau Kanban structuré en 5 colonnes :
|Colonne|Description|
|:-----:|:---------:|
|`Backlog`|Fourre-Issue: Répertoire des idées et besoins : toutes les issues y sont déposées avant leur sélection pour une itération|
|`Select for iteration`|Liste des issues planifiées pour l’itération en cours (objectifs de sprint)|
|`In progress`|Issues en cours de développement|
|`In review`|Issues associées à une pull request en attente de revue|
|`Done`|Issues finalisées et validées|

Ce système garantit une vision claire et partagée de l’avancement du projet, en permettant de visualiser en temps réel les priorités et l’état des travaux.

## Méthodologie de travail

### Découpage en itérations

Le projet est organisé en cycles(sprints) d'une semaine, au cours desquels certaines issues du Backlog sont sélectionnées pour être réalisées.

Si toutes les issues sélectionnées pour une itération ne sont pas finalisées, celles qui ne sont pas terminées à la fin de la semaine sont reportées au Backlog.
Si toutes les issues sélectionnées pour une itération sont finalisées, une release est créée et une nouvelle itération est planifiée.

### Attribution des tâches

Chaque issue est assignée à un ou plusieurs membres de l’équipe afin de répartir équitablement la charge de travail.

### Revue de code

Avant d’être fusionnée dans la branche principale, toute contribution passe par une review réalisée par au moins un autre membre de l’équipe.

### Intégration continue

Des tests automatiques et vérifications sont déclenchés à chaque pull request pour garantir la stabilité du projet

## Communication et collaboration

### Réunions journalières

Brèves réunions de suivi quotidienne pour partager l’avancée, identifier les blocages, fixer les objectifs du jour et le partager au corps enseignant

### Outils complémentaires

Utilisation de Telegram pour la communication instantanée et les échanges rapides.

## Bénéfices du processus

- Vision claire de l’état du projet grâce au Kanban.

- Suivi transparent de la charge et de l’avancement de chaque membre.

- Amélioration de la qualité grâce aux revues de code systématiques.

- Réduction des risques d’oubli ou de confusion grâce à la centralisation des tâches et décisions.

- Flexibilité pour s’adapter aux imprévus en réorganisant les priorités d’une itération à l’autre.
