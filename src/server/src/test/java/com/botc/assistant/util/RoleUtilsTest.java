package com.botc.assistant.util;

import com.botc.assistant.roles.RoleType;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for RoleUtils.
 */
class RoleUtilsTest {

    @Test
    void testCalculateRoleDistribution_5Players() {
        Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(5);

        assertEquals(3, distribution.get(RoleType.TOWNSFOLK));
        assertEquals(0, distribution.get(RoleType.OUTSIDER));
        assertEquals(1, distribution.get(RoleType.MINION));
        assertEquals(1, distribution.get(RoleType.DEMON));
    }

    @Test
    void testCalculateRoleDistribution_7Players() {
        Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(7);

        // For 7 players: 1 outsider, 1 minion, 1 demon = 3 evil/outsider roles
        // So 7 - 3 = 4 townsfolk
        assertEquals(4, distribution.get(RoleType.TOWNSFOLK));
        assertEquals(1, distribution.get(RoleType.OUTSIDER));
        assertEquals(1, distribution.get(RoleType.MINION));
        assertEquals(1, distribution.get(RoleType.DEMON));
    }

    @Test
    void testCalculateRoleDistribution_10Players() {
        Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(10);

        // For 10 players: 2 outsiders, 2 minions, 1 demon = 5 evil/outsider roles
        // So 10 - 5 = 5 townsfolk
        assertEquals(5, distribution.get(RoleType.TOWNSFOLK));
        assertEquals(2, distribution.get(RoleType.OUTSIDER));
        assertEquals(2, distribution.get(RoleType.MINION));
        assertEquals(1, distribution.get(RoleType.DEMON));
    }

    @Test
    void testCalculateRoleDistribution_15Players() {
        Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(15);

        // For 15 players: 3 outsiders, 3 minions, 1 demon = 7 evil/outsider roles
        // So 15 - 7 = 8 townsfolk (not 9)
        assertEquals(8, distribution.get(RoleType.TOWNSFOLK));
        assertEquals(3, distribution.get(RoleType.OUTSIDER));
        assertEquals(3, distribution.get(RoleType.MINION));
        assertEquals(1, distribution.get(RoleType.DEMON));
    }

    @Test
    void testCalculateRoleDistribution_InvalidPlayerCount() {
        assertThrows(IllegalArgumentException.class, () -> {
            RoleUtils.calculateRoleDistribution(4);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            RoleUtils.calculateRoleDistribution(21);
        });
    }

    @Test
    void testIsValidPlayerCount() {
        assertFalse(RoleUtils.isValidPlayerCount(4));
        assertTrue(RoleUtils.isValidPlayerCount(5));
        assertTrue(RoleUtils.isValidPlayerCount(10));
        assertTrue(RoleUtils.isValidPlayerCount(20));
        assertFalse(RoleUtils.isValidPlayerCount(21));
    }

    @Test
    void testGetMinPlayerCount() {
        assertEquals(5, RoleUtils.getMinPlayerCount());
    }

    @Test
    void testGetMaxPlayerCount() {
        assertEquals(20, RoleUtils.getMaxPlayerCount());
    }

    @Test
    void testRoleDistributionSumsCorrectly() {
        for (int playerCount = 5; playerCount <= 20; playerCount++) {
            Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(playerCount);

            int total = distribution.values().stream().mapToInt(Integer::intValue).sum();
            assertEquals(playerCount, total,
                    "Role distribution for " + playerCount + " players should sum to " + playerCount);
        }
    }

    @Test
    void testAlwaysOneDemon() {
        for (int playerCount = 5; playerCount <= 20; playerCount++) {
            Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(playerCount);
            assertEquals(1, distribution.get(RoleType.DEMON),
                    "Should always have exactly 1 demon for " + playerCount + " players");
        }
    }

    @Test
    void testTownsfolkAlwaysPositive() {
        for (int playerCount = 5; playerCount <= 20; playerCount++) {
            Map<RoleType, Integer> distribution = RoleUtils.calculateRoleDistribution(playerCount);
            assertTrue(distribution.get(RoleType.TOWNSFOLK) > 0,
                    "Should always have at least 1 townsfolk for " + playerCount + " players");
        }
    }
}
