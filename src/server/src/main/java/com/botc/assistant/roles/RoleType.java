package com.botc.assistant.roles;

/**
 * Enumeration of the different role types in Blood on the Clock Tower.
 */
public enum RoleType {
    /**
     * Townsfolk - good team members with helpful abilities
     */
    TOWNSFOLK("Townsfolk"),
    
    /**
     * Outsiders - good team members with harmful or limited abilities
     */
    OUTSIDER("Outsider"),
    
    /**
     * Minions - evil team members who support the demon
     */
    <PERSON><PERSON><PERSON>("Minion"),
    
    /**
     * Demons - evil team members who kill at night
     */
    DEMON("Demon");
    
    private final String displayName;
    
    RoleType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
