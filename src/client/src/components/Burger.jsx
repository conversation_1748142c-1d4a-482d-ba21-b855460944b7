import React, { useState } from 'react';
import { Link } from "react-router-dom";
import MenuIcon from '@mui/icons-material/Menu';

export default function MenuBurger({ status, mj }) {
  const [isOpen, setIsOpen] = useState(false);
  const login = status;
  const isMJ = mj;

  return (
    <nav className={`menu-burger ${isOpen ? 'isOpen' : ''}`}>
      <button className="burger-button" onClick={() => setIsOpen(!isOpen)}>
        <MenuIcon sx={{ color: "rgb(255, 251, 157)", fontSize: 60 }} />
      </button>

      <div className="menu-links">
        <Link to="/login" className="menu-link" id="login-link">Login</Link>
        <Link to="/regles" className="menu-link">Règles</Link>
        <Link to="/roles" className="menu-link">Rôles</Link>
        {login ? (
          isMJ ? (
            <>
              <Link to="/tablemj" className="menu-link">Table MJ</Link>
              <Link to="/tour" className="menu-link">Tour</Link>
            </>
          ) : (
            <>
              <Link to="/table" className="menu-link">Table</Link>
              <Link to="/partie" className="menu-link">Partie</Link>
            </>
          )
        ) : null}
      </div>
    </nav>
  );
}
