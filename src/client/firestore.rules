rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection rules
    match /users/{userId} {
      // Allow users to read and write their own profile
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow authenticated users to read other users' profiles (for pseudo lookup)
      // This is needed for the login functionality that looks up users by pseudo
      allow read: if request.auth != null;
      
      // Allow creation of new user profiles during registration
      allow create: if request.auth != null && request.auth.uid == userId;
    }
    
    // Add other collection rules here as needed
    // For example, if you have a games collection:
    // match /games/{gameId} {
    //   allow read, write: if request.auth != null;
    // }
  }
}
