import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Footer from "../components/Footer";
import NavBar from "../components/NavBar";
import { loginUser, getAuthErrorMessage } from '../services/authService';
import { getUserProfile, updateNarratorStatus } from '../services/userService';
import { auth } from '../config/firebase';

export default function Login() {
    const [pseudo, setPseudo] = useState('');
    const [password, setPassword] = useState('');
    const [isNarrator, setIsNarrator] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Validate inputs
            if (!pseudo.trim() || !password.trim()) {
                throw new Error('Veuillez remplir tous les champs.');
            }

            // Try to login with pseudo as email first (in case user enters email)
            let loginEmail = pseudo.trim();
            let userProfile = null;

            try {
                // First attempt: try direct login if pseudo is actually an email
                await loginUser(loginEmail, password);

                // If successful, get user profile to check narrator status
                const currentUser = auth.currentUser;
                if (currentUser) {
                    userProfile = await getUserProfile(currentUser.uid);
                }
            } catch (firstError) {
                // If direct login fails, it might be a pseudo, so we need to find the email
                // But we can't query Firestore without authentication, so we'll try a different approach

                // Check if the error is about user not found, which means it might be a pseudo
                if (firstError.code === 'auth/user-not-found' || firstError.code === 'auth/invalid-email') {
                    // For now, we'll ask users to use their email address for login
                    // This is a temporary solution until we implement a better pseudo lookup system
                    throw new Error('Veuillez utiliser votre adresse email pour vous connecter. Le login par pseudo sera disponible prochainement.');
                } else {
                    // Re-throw other authentication errors
                    throw firstError;
                }
            }

            // Update narrator status if it has changed
            if (userProfile.isNarrator !== isNarrator) {
                await updateNarratorStatus(userProfile.id, isNarrator);
            }

            // Redirect to home page or dashboard
            navigate('/');
        } catch (err) {
            console.error('Login error:', err);
            if (err.code) {
                setError(getAuthErrorMessage(err.code));
            } else {
                setError(err.message || 'Une erreur inattendue s\'est produite.');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <NavBar />
            <main id="login_page">
                <section id="login">
                    <div className="login-card">
                        <h1>Se connecter</h1>

                        {error && (
                            <div className="error-message">
                                {error}
                            </div>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label htmlFor="pseudo">Email</label>
                                <input
                                    type="email"
                                    id="pseudo"
                                    name="pseudo"
                                    placeholder="Adresse email"
                                    value={pseudo}
                                    onChange={(e) => setPseudo(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="password">Mot de passe</label>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    placeholder="Mot de passe"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group toggle-group">
                                <label htmlFor="narrator-toggle">Être narrateur</label>
                                <div className="toggle-switch">
                                    <input
                                        type="checkbox"
                                        id="narrator-toggle"
                                        checked={isNarrator}
                                        onChange={(e) => setIsNarrator(e.target.checked)}
                                        disabled={loading}
                                    />
                                    <span className="slider"></span>
                                </div>
                            </div>

                            <button type="submit" className="confirm-button" disabled={loading}>
                                {loading ? 'Connexion...' : 'Confirmer'}
                            </button>
                            <Link to="/register" className="create-account-link">
                                Créer un compte
                            </Link>
                        </form>
                    </div>
                </section>
            </main>
            <Footer />
        </>
    );
}
