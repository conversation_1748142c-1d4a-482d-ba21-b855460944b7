- Description du projet (objectifs, requirements fonctionnels, requirements non-fonctionnels)
- Description préliminaire de l'architecture
- Mockups (Figma, papier-cray, etc)/Landing page
- Description des choix techniques
- Description du processus de travail (git flow, devops, etc)
- Mise en place des outils de développement (issue tracker, ...)
- Mise en place d'un environnement de déploiement
- Mise en place d'un pipeline de livraison et de déploiement (CI/CD)
- Démonstration du déploiement d'une modification
