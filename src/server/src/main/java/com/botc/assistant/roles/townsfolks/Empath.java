package com.botc.assistant.roles.townsfolks;

/**
 * Empath - Townsfolk role
 * 
 * Each night, you learn how many of your 2 alive neighbors are evil.
 * 
 * The Empath learns information about their living neighbors each night.
 * They learn how many of their two alive neighbors (the players sitting
 * immediately to their left and right) are evil.
 */
public class Empath extends TownsFolk {
    
    public static final String ROLE_ID = "empath";
    public static final String ROLE_NAME = "Empath";
    public static final String ROLE_DESCRIPTION = "Each night, you learn how many of your 2 alive neighbors are evil.";
    
    private Empath(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Empath create() {
        return builder().build();
    }
    
    /**
     * Builder for Empath role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(10); // Each night information
            setupInfo("Learns how many of their 2 alive neighbors are evil each night");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Empath build() {
            return new Empath(this);
        }
    }
}
