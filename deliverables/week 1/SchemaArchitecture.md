# Schéma d'Architecture - Blood on the Clocktower Assistant

## 🏗️ Vue d'ensemble de l'Architecture

Le Blood on the Clocktower Assistant suit une **architecture client-serveur temps réel** conçue pour supporter des parties multijoueurs avec communication en temps réel entre joueurs et conteur (Storyteller).

## 📊 Diagramme d'Architecture Système

```mermaid
graph TB
    subgraph "👥 Client Applications"
        subgraph "🎭 Player Interface"
            PI[Interface Joueur<br/>- Lobby de partie<br/>- Vue rôle personnel<br/>- Interface de vote<br/>- Chat de discussion]
        end

        subgraph "📖 Storyteller Interface"
            SI[Interface Conteur<br/>- Tableau de bord complet<br/>- Gestion des phases<br/>- Attribution des rôles<br/>- Contrôle du jeu]
        end
    end

    subgraph "🔗 Communication Layer"
        subgraph "Real-time Communication"
            WS[WebSocket Server<br/>Événements temps réel]
            API[REST API<br/>Actions CRUD]
        end

        subgraph "Authentication & Sessions"
            AUTH[Service d'Authentification]
            SESS[Gestionnaire de Sessions]
        end
    end

    subgraph "🖥️ Server Layer"
        subgraph "🎯 Controllers"
            AC[AuthController<br/>Connexion/Inscription]
            RC[RoleController<br/>Gestion des rôles]
            GC[GameController<br/>CRUD des parties]
            PC[PlayerController<br/>Gestion des joueurs]
            WSC[WebSocketController<br/>Événements temps réel]
        end

        subgraph "⚙️ Game Services"
            GS[GameService<br/>Logique de partie]
            RS[RoleService<br/>Logique des rôles]
            PS[PhaseService<br/>Gestion des phases]
            VS[VotingService<br/>Système de vote]
            NS[NotificationService<br/>Notifications temps réel]
        end

        subgraph "💾 Data Layer"
            GR[GameRepository<br/>Persistance parties]
            PR[PlayerRepository<br/>Données joueurs]
            RR[RoleRepository<br/>Catalogue des rôles]
            UR[UserRepository<br/>Comptes utilisateurs]
        end

        subgraph "🎭 Domain Models"
            subgraph "Game State Management"
                GM[Game Model<br/>État de la partie]
                PM[Player Model<br/>État des joueurs]
                UM[User Model<br/>Comptes utilisateurs]
                VM[Vote Model<br/>Système de vote]
            end

            subgraph "Roles Hierarchy"
                ROLE[Role Base Class<br/>Capacités et règles]
                TF[Townsfolk<br/>13 rôles villageois]
                OUT[Outsider<br/>4 rôles étrangers]
                MIN[Minion<br/>4 rôles sbires]
                DEM[Demon<br/>3 rôles démons]
            end
        end
    end

    %% Client to Server Connections
    PI --> |"WebSocket<br/>Temps réel"| WS
    PI --> |"HTTP/REST<br/>Actions"| API
    SI --> |"WebSocket<br/>Contrôle jeu"| WS
    SI --> |"HTTP/REST<br/>Gestion"| API

    %% Authentication Flow
    PI --> AUTH
    SI --> AUTH
    AUTH --> SESS

    %% API Routing
    API --> AC
    API --> RC
    API --> GC
    API --> PC
    WS --> WSC

    %% Service Layer
    AC --> AUTH
    GC --> GS
    PC --> GS
    RC --> RS
    WSC --> NS
    WSC --> GS

    GS --> PS
    GS --> VS
    PS --> NS
    VS --> NS

    %% Data Access
    GS --> GR
    GS --> PR
    RS --> RR
    AUTH --> UR

    %% Domain Models
    GR --> GM
    PR --> PM
    UR --> UM
    VS --> VM
    RR --> ROLE

    %% Role Hierarchy
    ROLE --> TF
    ROLE --> OUT
    ROLE --> MIN
    ROLE --> DEM
```

## 🔄 Flux de Données - Scénarios de Jeu

### 🎮 Flux 1: Création et Rejoindre une Partie

```mermaid
sequenceDiagram
    participant ST as 📖 Storyteller
    participant P as 🎭 Player
    participant UI as 🌐 Interface
    participant API as 🔗 REST API
    participant WS as ⚡ WebSocket
    participant GS as ⚙️ GameService
    participant DB as 💾 Database

    ST->>UI: Créer nouvelle partie
    UI->>API: POST /api/games
    API->>GS: createGame()
    GS->>DB: Sauvegarder partie
    DB-->>GS: Game ID
    GS-->>API: Partie créée
    API-->>UI: Code de partie
    UI-->>ST: Afficher code partie

    P->>UI: Rejoindre avec code
    UI->>API: POST /api/games/{id}/join
    API->>GS: addPlayer()
    GS->>WS: Notifier nouveau joueur
    WS-->>ST: Mise à jour lobby
    WS-->>P: Confirmation rejointe
```

### 🌙 Flux 2: Phase de Nuit - Actions des Rôles

```mermaid
sequenceDiagram
    participant ST as 📖 Storyteller
    participant P as 🎭 Player (avec rôle)
    participant WS as ⚡ WebSocket
    participant PS as ⚙️ PhaseService
    participant RS as ⚙️ RoleService
    participant NS as 📢 NotificationService

    ST->>WS: Démarrer phase nuit
    WS->>PS: startNightPhase()
    PS->>RS: getActiveNightRoles()
    RS-->>PS: Liste rôles actifs
    PS->>NS: Notifier rôles actifs
    NS->>WS: Envoyer notifications
    WS-->>P: "Votre tour d'agir"

    P->>WS: Action de rôle
    WS->>RS: executeRoleAction()
    RS->>PS: Enregistrer action
    PS->>NS: Action complétée
    NS->>WS: Notifier storyteller
    WS-->>ST: Résultat action
```

### 🗳️ Flux 3: Phase de Vote

```mermaid
sequenceDiagram
    participant ST as 📖 Storyteller
    participant P1 as 🎭 Player 1
    participant P2 as 🎭 Player 2
    participant WS as ⚡ WebSocket
    participant VS as 🗳️ VotingService
    participant NS as 📢 NotificationService

    ST->>WS: Ouvrir nominations
    WS->>VS: startNominations()
    VS->>NS: Phase nominations ouverte
    NS->>WS: Notifier tous joueurs
    WS-->>P1: "Nominations ouvertes"
    WS-->>P2: "Nominations ouvertes"

    P1->>WS: Nominer P2
    WS->>VS: nominatePlayer(P2)
    VS->>NS: Nouvelle nomination
    NS->>WS: Diffuser nomination
    WS-->>ST: P1 nomine P2
    WS-->>P2: "Vous êtes nominé"

    ST->>WS: Démarrer vote
    WS->>VS: startVoting()
    VS->>NS: Vote en cours
    NS->>WS: Notifier vote
    WS-->>P1: Interface de vote
    WS-->>P2: Interface de vote

    P1->>WS: Vote (Oui/Non)
    P2->>WS: Vote (Oui/Non)
    WS->>VS: recordVote()
    VS->>VS: Calculer résultat
    VS->>NS: Résultat vote
    NS->>WS: Diffuser résultat
    WS-->>ST: Résultat final
```

## 🏛️ Architecture en Couches

### 1. **Couche Présentation (Client)**
- **Technologie**: React + Vite + WebSocket Client
- **Responsabilités**: Interfaces utilisateur différenciées, gestion d'état temps réel
- **Composants Joueur**:
  - `GameLobby` - Rejoindre/créer parties
  - `PlayerBoard` - Vue personnelle du joueur
  - `VotingInterface` - Interface de vote et nominations
  - `ChatComponent` - Communication entre joueurs
- **Composants Storyteller**:
  - `StorytellerDashboard` - Vue d'ensemble de la partie
  - `RoleAssignment` - Attribution des rôles
  - `PhaseManager` - Contrôle des phases de jeu
  - `GameMaster` - Outils de gestion avancés

### 2. **Couche Communication Temps Réel**
- **Technologie**: WebSocket + REST API
- **Responsabilités**: Communication bidirectionnelle, synchronisation d'état
- **WebSocket Events**:
  - `game.player.joined` - Nouveau joueur
  - `game.phase.changed` - Changement de phase
  - `game.vote.started` - Début de vote
  - `game.role.action` - Action de rôle
- **REST Endpoints**:
  - `/api/auth` - Authentification
  - `/api/games` - CRUD des parties
  - `/api/players` - Gestion des joueurs
  - `/api/roles` - Catalogue des rôles

### 3. **Couche Service (Logique Métier)**
- **Responsabilités**: Règles du jeu, orchestration des phases, validation
- **Services Principaux**:
  - `GameService` - Logique centrale des parties
  - `PhaseService` - Gestion des phases (jour/nuit)
  - `VotingService` - Système de vote et nominations
  - `RoleService` - Capacités et interactions des rôles
  - `NotificationService` - Diffusion d'événements temps réel

### 4. **Couche Authentification & Sessions**
- **Responsabilités**: Sécurité, gestion des utilisateurs, sessions de jeu
- **Composants**:
  - `AuthenticationService` - Connexion/inscription
  - `SessionManager` - Sessions utilisateur
  - `GameSessionManager` - Sessions de partie
  - `PermissionService` - Autorisations (joueur vs storyteller)

### 5. **Couche Accès Données**
- **Responsabilités**: Persistance, requêtes, cache
- **Repositories**:
  - `UserRepository` - Comptes utilisateurs
  - `GameRepository` - États des parties
  - `PlayerRepository` - Données des joueurs
  - `RoleRepository` - Catalogue des rôles

### 6. **Couche Domaine**
- **Responsabilités**: Modèles métier, règles du jeu Blood on the Clocktower
- **Modèles Principaux**:
  - `Game` - État complet d'une partie
  - `Player` - Joueur avec rôle et statut
  - `Role` - Hiérarchie des 24 rôles du jeu
  - `Vote` - Système de nominations et votes
  - `GamePhase` - Phases jour/nuit avec règles spécifiques

## 🎭 Modèle de Domaine Blood on the Clocktower

```mermaid
classDiagram
    class Role {
        <<abstract>>
        +String id
        +String name
        +String description
        +RoleType type
        +RoleTeam team
        +boolean hasNightAbility
        +boolean hasDayAbility
        +int nightPriority
        +executeNightAction()
        +executeDayAction()
    }
    
    class Townsfolk {
        +RoleType: TOWNSFOLK
        +RoleTeam: GOOD
    }
    
    class Outsider {
        +RoleType: OUTSIDER
        +RoleTeam: GOOD
    }
    
    class Minion {
        +RoleType: MINION
        +RoleTeam: EVIL
    }
    
    class Demon {
        +RoleType: DEMON
        +RoleTeam: EVIL
    }
    
    class User {
        +String id
        +String username
        +String email
        +Date createdAt
        +UserRole userRole
        +List~GameSession~ sessions
    }

    class Game {
        +String id
        +String code
        +int maxPlayers
        +int currentPlayers
        +Date createdAt
        +GameState state
        +GamePhase currentPhase
        +User storyteller
        +List~Player~ players
        +VotingSession currentVoting
        +boolean isActive
    }

    class Player {
        +String id
        +User user
        +String name
        +int position
        +Role role
        +boolean alive
        +boolean canVote
        +boolean nominated
        +boolean hasVoted
        +List~String~ neighbors
        +PlayerStatus status
    }

    class Vote {
        +String id
        +Player nominator
        +Player nominee
        +Map~Player,VoteChoice~ votes
        +VoteResult result
        +Date timestamp
        +boolean isActive
    }

    class GamePhase {
        +PhaseType type
        +int dayNumber
        +Date startTime
        +List~Role~ activeRoles
        +PhaseStatus status
    }

    class GameSession {
        +String sessionId
        +User user
        +Game game
        +Date joinedAt
        +boolean isActive
        +SessionType type
    }

    Role <|-- Townsfolk
    Role <|-- Outsider
    Role <|-- Minion
    Role <|-- Demon

    User "1" --> "many" GameSession
    Game "1" *-- "many" Player
    Game "1" --> "1" User : storyteller
    Game "1" *-- "many" Vote
    Game "1" --> "1" GamePhase
    Player "1" --> "1" User
    Player "1" --> "1" Role
    Vote "1" --> "2" Player : nominator/nominee
    GameSession "1" --> "1" User
    GameSession "1" --> "1" Game
```

## 🔧 Architecture Technique

### **Frontend (React + Vite + WebSocket)**
```
src/client/
├── src/
│   ├── components/
│   │   ├── auth/
│   │   │   ├── LoginForm.jsx              # Connexion utilisateur
│   │   │   └── RegisterForm.jsx           # Inscription
│   │   ├── game/
│   │   │   ├── GameLobby.jsx              # Lobby de partie
│   │   │   ├── PlayerBoard.jsx            # Interface joueur
│   │   │   ├── StorytellerDashboard.jsx   # Interface conteur
│   │   │   ├── VotingInterface.jsx        # Système de vote
│   │   │   ├── RoleCard.jsx               # Affichage des rôles
│   │   │   └── GameChat.jsx               # Chat de partie
│   │   ├── ui/
│   │   │   ├── PlayerList.jsx             # Liste des joueurs
│   │   │   ├── PhaseIndicator.jsx         # Indicateur de phase
│   │   │   └── NotificationPanel.jsx      # Notifications
│   │   └── layout/
│   │       ├── Header.jsx                 # En-tête navigation
│   │       └── Footer.jsx                 # Pied de page
│   ├── hooks/
│   │   ├── useWebSocket.js                # Hook WebSocket
│   │   ├── useGameState.js                # État de partie
│   │   └── useAuth.js                     # Authentification
│   ├── services/
│   │   ├── api.js                         # Client API REST
│   │   ├── websocket.js                   # Client WebSocket
│   │   └── gameLogic.js                   # Logique côté client
│   ├── utils/
│   │   ├── constants.js                   # Constantes du jeu
│   │   └── helpers.js                     # Fonctions utilitaires
│   ├── App.jsx                            # Composant racine
│   └── main.jsx                           # Point d'entrée
├── package.json                           # Dépendances (React, Socket.io)
└── vite.config.js                         # Configuration Vite
```

### **Backend (Spring Boot + WebSocket + JPA)**
```
src/server/
├── src/main/java/com/botc/assistant/
│   ├── Application.java                   # Point d'entrée Spring Boot
│   ├── config/
│   │   ├── WebSocketConfig.java           # Configuration WebSocket
│   │   ├── SecurityConfig.java            # Configuration sécurité
│   │   └── DatabaseConfig.java            # Configuration base de données
│   ├── controller/
│   │   ├── AuthController.java            # Authentification
│   │   ├── GameController.java            # API des parties
│   │   ├── PlayerController.java          # API des joueurs
│   │   ├── RoleController.java            # API des rôles
│   │   └── WebSocketController.java       # Événements temps réel
│   ├── service/
│   │   ├── AuthService.java               # Service authentification
│   │   ├── GameService.java               # Logique des parties
│   │   ├── PhaseService.java              # Gestion des phases
│   │   ├── VotingService.java             # Système de vote
│   │   ├── RoleService.java               # Logique des rôles
│   │   ├── NotificationService.java       # Notifications temps réel
│   │   └── UserService.java               # Gestion utilisateurs
│   ├── repository/
│   │   ├── UserRepository.java            # Persistance utilisateurs
│   │   ├── GameRepository.java            # Persistance parties
│   │   ├── PlayerRepository.java          # Persistance joueurs
│   │   └── RoleRepository.java            # Catalogue des rôles
│   ├── model/
│   │   ├── User.java                      # Modèle Utilisateur
│   │   ├── Game.java                      # Modèle Partie
│   │   ├── Player.java                    # Modèle Joueur
│   │   ├── Vote.java                      # Modèle Vote
│   │   └── GameSession.java               # Modèle Session
│   ├── security/
│   │   ├── JwtTokenProvider.java          # Gestion JWT
│   │   └── UserPrincipal.java             # Principal utilisateur
│   └── roles/                             # Hiérarchie des rôles
│       ├── Role.java                      # Classe de base
│       ├── townsfolks/                    # 13 rôles Villageois
│       ├── outsiders/                     # 4 rôles Étrangers
│       ├── minions/                       # 4 rôles Sbires
│       └── demons/                        # 3 rôles Démons
├── src/main/resources/
│   ├── application.yml                    # Configuration Spring
│   └── db/migration/                      # Scripts de migration DB
└── pom.xml                               # Dépendances (Spring, WebSocket, JPA)
```

## 🌐 Communication et Protocoles

### **API REST Endpoints**

#### **Authentification**
- `POST /api/auth/register` - Inscription utilisateur
- `POST /api/auth/login` - Connexion utilisateur
- `POST /api/auth/logout` - Déconnexion
- `GET /api/auth/me` - Profil utilisateur actuel

#### **Gestion des Parties**
- `POST /api/games` - Créer une nouvelle partie
- `GET /api/games/{code}` - Rejoindre partie avec code
- `GET /api/games/{id}` - Détails d'une partie
- `PUT /api/games/{id}/start` - Démarrer la partie
- `DELETE /api/games/{id}` - Supprimer la partie

#### **Gestion des Joueurs**
- `POST /api/games/{id}/join` - Rejoindre une partie
- `DELETE /api/games/{id}/leave` - Quitter une partie
- `PUT /api/games/{id}/players/{playerId}` - Modifier statut joueur

#### **Système de Rôles**
- `GET /api/roles` - Catalogue complet des rôles
- `GET /api/roles/statistics` - Statistiques par type
- `POST /api/games/{id}/assign-roles` - Attribution automatique
- `PUT /api/games/{id}/roles/{playerId}` - Assigner rôle spécifique

### **WebSocket Events (Temps Réel)**

#### **Événements de Partie**
- `game.created` - Nouvelle partie créée
- `game.player.joined` - Joueur rejoint la partie
- `game.player.left` - Joueur quitte la partie
- `game.started` - Partie démarrée
- `game.ended` - Partie terminée

#### **Événements de Phase**
- `phase.day.started` - Début phase jour
- `phase.night.started` - Début phase nuit
- `phase.discussion.started` - Début discussion
- `phase.voting.started` - Début des votes

#### **Événements de Vote**
- `nomination.created` - Nouvelle nomination
- `vote.started` - Vote ouvert
- `vote.cast` - Vote enregistré
- `vote.completed` - Vote terminé
- `player.executed` - Joueur exécuté

#### **Événements de Rôle**
- `role.assigned` - Rôle attribué
- `role.action.requested` - Action de rôle demandée
- `role.action.completed` - Action de rôle terminée
- `player.died` - Joueur mort (nuit)

### **Sécurité et Authentification**
- **JWT Tokens**: Authentification stateless
- **Session Management**: Sessions de jeu sécurisées
- **Role-Based Access**: Permissions joueur vs storyteller
- **CORS Configuration**: Origins de production autorisées
- **Rate Limiting**: Protection contre le spam
- **Input Validation**: Validation côté serveur

## 🎯 Patterns Architecturaux Utilisés

1. **MVC (Model-View-Controller)**: Séparation claire des responsabilités
2. **Repository Pattern**: Abstraction de l'accès aux données avec JPA
3. **Factory Pattern**: Création des instances de rôles
4. **Service Layer**: Encapsulation de la logique métier complexe
5. **Observer Pattern**: WebSocket pour notifications temps réel
6. **Command Pattern**: Actions de rôles et commandes de jeu
7. **State Machine**: Gestion des phases de jeu (jour/nuit/vote)
8. **Component-Based Architecture**: React avec composants réutilisables
9. **Pub/Sub Pattern**: Système d'événements pour notifications
10. **Authentication & Authorization**: JWT + Role-based access

## 🔄 Gestion des États

### **Frontend (React + WebSocket)**
- **React Context**: État global de l'application (utilisateur, partie)
- **Custom Hooks**:
  - `useWebSocket` - Connexion temps réel
  - `useGameState` - État de la partie courante
  - `useAuth` - État d'authentification
- **Local State**: États spécifiques aux composants
- **Real-time Sync**: Synchronisation automatique via WebSocket

### **Backend (Spring Boot + JPA)**
- **JPA Entities**: Persistance des données avec relations
- **Service Layer**: Gestion d'état métier avec transactions
- **Session Management**: Sessions utilisateur et de jeu
- **Event-Driven**: Propagation d'événements via WebSocket
- **Database Transactions**: Cohérence des données critiques

## 🎮 Expérience Utilisateur

### **Interface Joueur**
- **Lobby Intuitif**: Rejoindre/créer parties facilement
- **Vue Personnalisée**: Informations spécifiques au rôle
- **Interactions Temps Réel**: Votes et discussions fluides
- **Notifications Contextuelles**: Alertes selon la phase de jeu
- **Interface Mobile-Friendly**: Responsive design

### **Interface Storyteller**
- **Dashboard Complet**: Vue d'ensemble de tous les joueurs
- **Contrôles Avancés**: Gestion fine des phases et événements
- **Outils de Modération**: Contrôle des discussions et votes
- **Historique des Actions**: Suivi complet de la partie
- **Aide Contextuelle**: Rappels des règles et capacités

## 🚀 Déploiement et Scalabilité

### **Environnement de Développement**
- **Frontend**: `npm run dev` (Vite dev server avec HMR)
- **Backend**: `./mvnw spring-boot:run` (Maven wrapper)
- **Base de Données**: H2 en mémoire pour développement rapide
- **WebSocket**: Connexions locales pour tests temps réel

### **Environnement de Production**
- **Containerisation**: Docker multi-stage avec optimisations
- **Frontend**: Build statique optimisé servi par Nginx
- **Backend**: JAR Spring Boot avec profil production
- **Base de Données**: PostgreSQL avec pool de connexions
- **Reverse Proxy**: Nginx avec SSL/TLS et load balancing
- **WebSocket**: Clustering avec Redis pour sessions partagées

### **Scalabilité et Performance**
- **Horizontal Scaling**: Multiples instances backend
- **Session Clustering**: Redis pour partage d'état entre instances
- **Database Optimization**: Index optimisés pour requêtes fréquentes
- **Caching Strategy**: Cache Redis pour données de rôles
- **CDN Integration**: Assets statiques via CDN
- **Monitoring**: Métriques temps réel et alertes

### **Sécurité Production**
- **HTTPS Obligatoire**: Certificats SSL/TLS automatiques
- **JWT Sécurisé**: Tokens avec expiration et refresh
- **Rate Limiting**: Protection DDoS et spam
- **Input Sanitization**: Validation stricte côté serveur
- **CORS Restrictif**: Origins de production uniquement
- **Security Headers**: CSP, HSTS, X-Frame-Options

## 🎯 Roadmap Technique

### **Phase 1: MVP (Minimum Viable Product)**
- ✅ Architecture de base client-serveur
- ✅ Modèles de données et rôles
- 🔄 Interface de base joueur/storyteller
- 🔄 Système d'authentification
- 🔄 WebSocket temps réel

### **Phase 2: Fonctionnalités Avancées**
- 📋 Système de vote complet
- 📋 Gestion des phases automatisée
- 📋 Interface storyteller avancée
- 📋 Historique et replay des parties
- 📋 Système de notifications push

### **Phase 3: Optimisation et Scalabilité**
- 📋 Clustering et load balancing
- 📋 Optimisations performance
- 📋 Monitoring et observabilité
- 📋 Tests de charge et stress
- 📋 Documentation API complète

### **Phase 4: Fonctionnalités Premium (si on a le temps)**
- 📋 Modes de jeu personnalisés
- 📋 Statistiques et analytics
- 📋 Système de classement
- 📋 Intégrations tierces (Discord, etc.)
- 📋 Application mobile native
- 📋 Chat textuel
- 📋 Chat vocal intégré

---

*Schéma d'architecture finale - Blood on the Clocktower Assistant*
*Vision produit pour application de jeu multijoueur temps réel*
