// User service for managing user data in Firestore
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { db } from '../config/firebase';

/**
 * Create user profile in Firestore
 * @param {string} userId - Firebase Auth user ID
 * @param {Object} userData - User data object
 * @param {string} userData.email - User's email
 * @param {string} userData.pseudo - User's pseudo/username
 * @param {string} userData.prenom - User's first name
 * @param {string} userData.nom - User's last name
 * @param {boolean} userData.isNarrator - Whether user is a narrator (optional, defaults to false)
 * @returns {Promise<void>}
 */
export const createUserProfile = async (userId, userData) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userProfile = {
      email: userData.email,
      pseudo: userData.pseudo,
      prenom: userData.prenom,
      nom: userData.nom,
      isNarrator: userData.isNarrator || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await setDoc(userRef, userProfile);
    return userProfile;
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
};

/**
 * Get user profile from Firestore
 * @param {string} userId - Firebase Auth user ID
 * @returns {Promise<Object|null>} User profile object or null if not found
 */
export const getUserProfile = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return { id: userSnap.id, ...userSnap.data() };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Update user profile in Firestore
 * @param {string} userId - Firebase Auth user ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<void>}
 */
export const updateUserProfile = async (userId, updateData) => {
  try {
    const userRef = doc(db, 'users', userId);
    const updatedData = {
      ...updateData,
      updatedAt: new Date()
    };
    
    await updateDoc(userRef, updatedData);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Check if pseudo is already taken
 * @param {string} pseudo - Pseudo to check
 * @param {string} excludeUserId - User ID to exclude from check (for updates)
 * @returns {Promise<boolean>} True if pseudo is taken, false otherwise
 */
export const isPseudoTaken = async (pseudo, excludeUserId = null) => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('pseudo', '==', pseudo));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return false;
    }
    
    // If excludeUserId is provided, check if the pseudo belongs to that user
    if (excludeUserId) {
      const docs = querySnapshot.docs;
      return docs.some(doc => doc.id !== excludeUserId);
    }
    
    return true;
  } catch (error) {
    console.error('Error checking pseudo availability:', error);
    throw error;
  }
};

/**
 * Get user profile by pseudo
 * @param {string} pseudo - User's pseudo
 * @returns {Promise<Object|null>} User profile object or null if not found
 */
export const getUserByPseudo = async (pseudo) => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('pseudo', '==', pseudo));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user by pseudo:', error);
    throw error;
  }
};

/**
 * Update user narrator status
 * @param {string} userId - Firebase Auth user ID
 * @param {boolean} isNarrator - New narrator status
 * @returns {Promise<void>}
 */
export const updateNarratorStatus = async (userId, isNarrator) => {
  try {
    await updateUserProfile(userId, { isNarrator });
  } catch (error) {
    console.error('Error updating narrator status:', error);
    throw error;
  }
};
