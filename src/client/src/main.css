main {
    display: flex;
    justify-content: center;
}

footer {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2B0F38;
    color: #FFFB9D;
    height: 80px;
}

section {
    padding-top: 20px;
    padding-bottom: 20px;
}


/* Nav bar home */
header {
    font-family: 'cc-goth-chic', sans-serif;
    padding-right: 2%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: #2B0F38;
}

header img {
    width: 50%;
}

#menu_options {
    min-width: 100px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
}

#menu_links {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

#menu_links a {
    font-size: 1.5em;
    text-decoration: none;
    color: #FFFB9D;
}

nav .link {
    display: none;
}

nav button {
    display: block;
}

/* If the menu is open, we'll display the links */
.isOpen a {
    display: block;
}

#login-link {
    border-bottom: #FFFB9D 2px solid;
    border-right: none;
    padding-right: 10px;
}

/* Landing page */

#landing {
    display: flex;
    flex-direction: column;
}

section h1 {
    font-family: 'cc-goth-chic', sans-serif;
    font-weight: 400;
    font-style: normal;
    text-align: center;
    padding-bottom: 20px;
}

section p {
    font-family: sans-serif;
    font-weight: 400;
    font-style: normal;
}

#mj_side_promo h1 {
    color: #795C4E;
}

.promo {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

#mj_side_promo .promo p {
    color: #4F3A2F;
}

.promo p {
    font-size: 20px;
    text-wrap: wrap;
    width: 90%;
}

.promo img {
    width: 50%;
}

#player_side_promo {
    background-image:
        linear-gradient(rgba(100, 97, 141, 0.8), rgba(100, 97, 141, 0.8)),
        url('./src/assets/paper.jpg');
    background-repeat: repeat;
    background-size: auto;
    background-color: #64618D;
}

#player_side_promo h1,
#player_side_promo p {
    color: white;
}

/* Mobile-first burger menu styles */
.menu-burger {
    position: relative;
}

.burger-button {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.menu-links {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #2B0F38;
    min-width: 200px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 10px 0;
    z-index: 1000;
}

.menu-burger.isOpen .menu-links {
    display: block;
}

.menu-link {
    display: block;
    padding: 12px 20px;
    color: #FFFB9D;
    text-decoration: none;
    font-size: 1.2em;
    transition: background-color 0.2s;
}

.menu-link:hover {
    background-color: rgba(255, 251, 157, 0.1);
}

/* Desktop styles */
@media (min-width: 1000px) {

    header img {
        width: 100%;
    }

    .burger-button {
        display: none;
    }

    .menu-links {
        display: flex !important;
        position: static;
        background: none;
        box-shadow: none;
        padding: 0;
        gap: 20px;
        align-items: center;
    }

    .menu-link {
        padding: 0;
        font-size: 1.5em;
    }

    .menu-link:hover {
        background: none;
        color: rgba(255, 251, 157, 0.8);
    }

    #login-link {
        border-right: #FFFB9D 2px solid;
        border-bottom: none;
    }


    /* Landing page*/

    #landing section {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: justify;
        padding-left: 10%;
        padding-right: 10%;
    }

    .promo {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 5%;
    }

    .promo p {
        width: 100%;
        /* au lieu de 90% */
        text-align: justify;
    }

    /* Rules page*/

    #rules {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: justify;
        padding-left: 10%;
        padding-right: 10%;
    }





}

/* Login / register page styles */
#login,
#register {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.login-card,
.register-card {
    background-color: #64618D;
    border-radius: 20px;
    padding: 40px 30px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.login-card h1,
.register-card h1 {
    font-family: 'cc-goth-chic', sans-serif;
    color: white;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2em;
    font-weight: 400;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-family: 'cc-goth-chic', sans-serif;
    color: white;
    display: block;
    margin-bottom: 8px;
    font-size: 1.2em;
    font-weight: 400;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    background-color: white;
    color: #333;
    box-sizing: border-box;
}

.form-group input[type="text"]::placeholder,
.form-group input[type="email"]::placeholder,
.form-group input[type="password"]::placeholder {
    color: #999;
    font-style: italic;
}

.toggle-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.toggle-group label {
    margin-bottom: 0;
}

.toggle-switch {
    position: relative;
    width: 60px;
    height: 30px;
}

.toggle-switch input[type="checkbox"] {
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    cursor: pointer;
    z-index: 1;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #4CAF50;
}

input:checked+.slider:before {
    transform: translateX(30px);
}

.confirm-button {
    width: 100%;
    padding: 15px;
    background-color: #FFFB9D;
    color: #333;
    border: none;
    border-radius: 8px;
    font-family: 'cc-goth-chic', sans-serif;
    font-size: 1.3em;
    font-weight: 400;
    cursor: pointer;
    margin-bottom: 15px;
    transition: background-color 0.3s;
}

.confirm-button:hover {
    background-color: #F5F0B8;
}

.create-account-link {
    display: block;
    text-align: center;
    color: white;
    text-decoration: none;
    font-size: 0.9em;
    margin-top: 10px;
}

.create-account-link:hover {
    text-decoration: underline;
}
