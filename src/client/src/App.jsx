import './App.css';

function App() {
  return (
    <div className="app">
      {/* Hero Section */}
      <header className="hero-section">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Blood on the Clocktower
              <span className="hero-subtitle">Assistant</span>
            </h1>
            <p className="hero-description">
              Votre compagnon ultime pour le jeu de déduction sociale le plus palpitant jamais créé.
              Naviguez dans les mystères de Ravenswood Bluff avec confiance et style.
            </p>
            <div className="hero-buttons">
              <button className="btn-primary">
                Commencer
              </button>
              <button className="btn-secondary">
                En savoir plus
              </button>
            </div>
          </div>
          <div className="hero-visual">
            <div className="clocktower-icon">
              <div className="clock-face">
                <div className="clock-hand hour-hand"></div>
                <div className="clock-hand minute-hand"></div>
                <div className="clock-center"></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* About Section */}
      <section className="about-section">
        <div className="container">
          <h2 className="section-title">Qu'est-ce que Blood on the Clocktower ?</h2>
          <div className="about-grid">
            <div className="about-card">
              <div className="card-icon">🎭</div>
              <h3>Déduction Sociale</h3>
              <p>Un jeu de mystère, de déduction et de bluff où le bien doit trouver et exécuter le démon avant que le mal ne prenne le contrôle.</p>
            </div>
            <div className="about-card">
              <div className="card-icon">🌙</div>
              <h3>Phases Jour & Nuit</h3>
              <p>Vivez la tension alors que la ville s'endort et que le mal s'éveille, chaque nuit apportant de nouvelles informations et dangers.</p>
            </div>
            <div className="about-card">
              <div className="card-icon">👥</div>
              <h3>Rôles Uniques</h3>
              <p>Chaque joueur a une capacité unique qui peut changer le cours du jeu, de l'humble Lavandière au puissant Démon.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container">
          <h2 className="section-title">Fonctionnalités de l'Assistant</h2>
          <div className="features-grid">
            <div className="feature-card">
              <h3>Référence des Rôles</h3>
              <p>Accès rapide à toutes les capacités des personnages, interactions et stratégies.</p>
            </div>
            <div className="feature-card">
              <h3>Minuteur de Jeu</h3>
              <p>Suivez les phases jour et nuit avec des minuteurs personnalisables.</p>
            </div>
            <div className="feature-card">
              <h3>Prise de Notes</h3>
              <p>Enregistrez les indices, soupçons et schémas de vote tout au long du jeu.</p>
            </div>
            <div className="feature-card">
              <h3>Créateur de Scripts</h3>
              <p>Créez et personnalisez vos propres scripts de jeu avec différentes combinaisons de personnages.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="app-footer">
        <div className="container">
          <p>Blood on the Clocktower Assistant • Créé avec ❤️ pour la communauté BotC</p>
          <p className="footer-note">
            Blood on the Clocktower est une marque déposée de The Pandemonium Institute.
            Cet assistant est un outil créé par des fans et n'est pas officiellement affilié.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
