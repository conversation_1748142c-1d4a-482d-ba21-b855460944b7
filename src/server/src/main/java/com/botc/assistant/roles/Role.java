package com.botc.assistant.roles;

/**
 * Base interface for all Blood on the Clock Tower roles.
 * Defines the core properties and behaviors that all roles must implement.
 */
public interface Role {
    
    /**
     * Gets the unique identifier for this role.
     * @return the role ID
     */
    String getId();
    
    /**
     * Gets the display name of this role.
     * @return the role name
     */
    String getName();
    
    /**
     * Gets the description of this role's ability.
     * @return the role description
     */
    String getDescription();
    
    /**
     * Gets the team this role belongs to.
     * @return the role team
     */
    RoleTeam getTeam();
    
    /**
     * Gets the type of this role within its team.
     * @return the role type
     */
    RoleType getType();
    
    /**
     * Indicates whether this role is unique (only one can exist in a game).
     * @return true if the role is unique, false otherwise
     */
    boolean isUnique();
    
    /**
     * Indicates whether this role has a night ability.
     * @return true if the role has a night ability, false otherwise
     */
    boolean hasNightAbility();
    
    /**
     * Indicates whether this role has a day ability.
     * @return true if the role has a day ability, false otherwise
     */
    boolean hasDayAbility();
    
    /**
     * Gets the priority order for night actions (lower numbers act first).
     * @return the night action priority, or null if no night ability
     */
    Integer getNightPriority();
    
    /**
     * Indicates whether this role can be affected by certain abilities.
     * @return true if the role can be targeted, false otherwise
     */
    boolean canBeTargeted();
    
    /**
     * Gets additional setup information for this role.
     * @return setup information or null if none
     */
    String getSetupInfo();
}
