import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Footer from "../components/Footer";
import NavBar from "../components/NavBar";
import { loginUser, getAuthErrorMessage } from '../services/authService';
import { getUserByPseudo, updateNarratorStatus } from '../services/userService';

export default function Login() {
    const [pseudo, setPseudo] = useState('');
    const [password, setPassword] = useState('');
    const [isNarrator, setIsNarrator] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Validate inputs
            if (!pseudo.trim() || !password.trim()) {
                throw new Error('Veu<PERSON>z remplir tous les champs.');
            }

            // Find user by pseudo to get their email
            const userProfile = await getUserByPseudo(pseudo.trim());
            if (!userProfile) {
                throw new Error('Aucun utilisateur trouvé avec ce pseudo.');
            }

            // Login with email and password
            await loginUser(userProfile.email, password);

            // Update narrator status if it has changed
            if (userProfile.isNarrator !== isNarrator) {
                await updateNarratorStatus(userProfile.id, isNarrator);
            }

            // Redirect to home page or dashboard
            navigate('/');
        } catch (err) {
            console.error('Login error:', err);
            if (err.code) {
                setError(getAuthErrorMessage(err.code));
            } else {
                setError(err.message || 'Une erreur inattendue s\'est produite.');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <NavBar />
            <main id="login_page">
                <section id="login">
                    <div className="login-card">
                        <h1>Se connecter</h1>

                        {error && (
                            <div className="error-message">
                                {error}
                            </div>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label htmlFor="pseudo">Pseudo</label>
                                <input
                                    type="text"
                                    id="pseudo"
                                    name="pseudo"
                                    placeholder="Pseudo"
                                    value={pseudo}
                                    onChange={(e) => setPseudo(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="password">Mot de passe</label>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    placeholder="Mot de passe"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group toggle-group">
                                <label htmlFor="narrator-toggle">Être narrateur</label>
                                <div className="toggle-switch">
                                    <input
                                        type="checkbox"
                                        id="narrator-toggle"
                                        checked={isNarrator}
                                        onChange={(e) => setIsNarrator(e.target.checked)}
                                        disabled={loading}
                                    />
                                    <span className="slider"></span>
                                </div>
                            </div>

                            <button type="submit" className="confirm-button" disabled={loading}>
                                {loading ? 'Connexion...' : 'Confirmer'}
                            </button>
                            <Link to="/register" className="create-account-link">
                                Créer un compte
                            </Link>
                        </form>
                    </div>
                </section>
            </main>
            <Footer />
        </>
    );
}
