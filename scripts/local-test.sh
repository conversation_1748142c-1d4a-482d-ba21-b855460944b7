#!/bin/bash

# Local Testing Script for Blood on the Clocktower Assistant
# This script builds and tests the application locally

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING:${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    warning "Docker Compose not found, using docker compose plugin"
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

log "Starting local testing of Blood on the Clocktower Assistant..."

# Clean up any existing containers
log "Cleaning up existing containers..."
docker stop botc-assistant-test 2>/dev/null || true
docker rm botc-assistant-test 2>/dev/null || true

# Build the Docker image
log "Building Docker image..."
if ! docker build -t botc-assistant:test .; then
    error "Failed to build Docker image"
    exit 1
fi

# Start the container
log "Starting container..."
docker run -d \
    --name botc-assistant-test \
    -p 8080:8080 \
    botc-assistant:test

# Wait for application to start
log "Waiting for application to start..."
timeout 60 bash -c 'until curl -f http://localhost:8080/ >/dev/null 2>&1; do sleep 2; done' || {
    error "Application failed to start within 60 seconds"
    docker logs botc-assistant-test
    docker stop botc-assistant-test
    docker rm botc-assistant-test
    exit 1
}

log "Application started successfully!"

# Test frontend
log "Testing frontend..."
info "Testing landing page..."
if curl -f http://localhost:8080/ >/dev/null 2>&1; then
    log "Frontend is accessible"
else
    error "Frontend test failed"
    exit 1
fi

# Test frontend (if accessible)
info "Testing frontend..."
if curl -f http://localhost:8080/ >/dev/null 2>&1; then
    log "Frontend is accessible"
else
    warning "Frontend test failed - this might be expected if static files aren't properly configured"
fi

# Show container logs
info "Container logs:"
docker logs botc-assistant-test --tail 20

# Cleanup
log "Cleaning up..."
docker stop botc-assistant-test
docker rm botc-assistant-test

# Clean up test data
rm -rf test-data

log "Local testing completed successfully! ✅"
info "The application is ready for deployment."
info ""
info "Next steps:"
info "1. Commit and push your changes to trigger CI/CD"
info "2. Set up your server using deploy/server-setup.sh"
info "3. Configure GitHub secrets for deployment"
info "4. Push to main branch to deploy"
