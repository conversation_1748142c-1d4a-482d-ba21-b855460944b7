package com.botc.assistant.dto;

/**
 * Data Transfer Object for Player information.
 * Represents a player in a Blood on the Clock Tower game.
 */
public class PlayerDto {
    
    private String id;
    private String name;
    private String roleId;
    private RoleDto role;
    private boolean isAlive;
    private boolean isNominated;
    private boolean hasVoted;
    private int position; // Seating position in the circle
    private String notes;
    
    // Default constructor
    public PlayerDto() {}
    
    // Constructor with basic info
    public PlayerDto(String id, String name, int position) {
        this.id = id;
        this.name = name;
        this.position = position;
        this.isAlive = true;
        this.isNominated = false;
        this.hasVoted = false;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRoleId() {
        return roleId;
    }
    
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
    
    public RoleDto getRole() {
        return role;
    }
    
    public void setRole(RoleDto role) {
        this.role = role;
        if (role != null) {
            this.roleId = role.getId();
        }
    }
    
    public boolean isAlive() {
        return isAlive;
    }
    
    public void setAlive(boolean alive) {
        isAlive = alive;
    }
    
    public boolean isNominated() {
        return isNominated;
    }
    
    public void setNominated(boolean nominated) {
        isNominated = nominated;
    }
    
    public boolean isHasVoted() {
        return hasVoted;
    }
    
    public void setHasVoted(boolean hasVoted) {
        this.hasVoted = hasVoted;
    }
    
    public int getPosition() {
        return position;
    }
    
    public void setPosition(int position) {
        this.position = position;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public String toString() {
        return String.format("PlayerDto{id='%s', name='%s', position=%d, role='%s', alive=%s}", 
                           id, name, position, roleId, isAlive);
    }
}
