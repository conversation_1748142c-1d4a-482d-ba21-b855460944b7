# CI/CD Deployment Guide

This guide provides complete instructions for setting up a CI/CD pipeline for the BotC Application using GitHub Actions, Docker, and automated deployment.

## 🏗️ Architecture Overview

The deployment pipeline consists of:

1. **Multi-stage Docker build** - Builds both React frontend and Spring Boot backend into a single optimized container
2. **GitHub Actions CI/CD** - Automated testing, building, and deployment
3. **GitHub Container Registry** - Stores Docker images
4. **Automated server deployment** - Zero-downtime deployments with health checks and rollback

## 📋 Prerequisites

- GitHub repository with the BotC application code
- Ubuntu/Debian server with root access
- Domain name (optional, for SSL/HTTPS)

## 🚀 Quick Start

### 1. Server Setup

Run the server setup script on your deployment server:

```bash
# Download and run the setup script
curl -fsSL https://raw.githubusercontent.com/your-username/your-repo/main/deploy/server-setup.sh -o server-setup.sh
chmod +x server-setup.sh

# Run as root (will create botcapp user and configure everything)
sudo ./server-setup.sh

# Optional: Add your SSH public key for GitHub Actions
sudo ./server-setup.sh "$(cat ~/.ssh/id_rsa.pub)"
```

### 2. GitHub Repository Configuration

#### A. Enable GitHub Container Registry

1. Go to your repository settings
2. Navigate to "Actions" → "General"
3. Under "Workflow permissions", select "Read and write permissions"
4. Check "Allow GitHub Actions to create and approve pull requests"

#### B. Configure Repository Secrets

Go to your repository → Settings → Secrets and variables → Actions, and add:

```
DEPLOY_HOST=your-server-ip-address
DEPLOY_USER=botcapp
DEPLOY_KEY=your-private-ssh-key
DEPLOY_PORT=22
```

To generate SSH keys for deployment:

```bash
# On your local machine
ssh-keygen -t rsa -b 4096 -C "github-actions-deploy"

# Copy the public key to your server
ssh-copy-id -i ~/.ssh/id_rsa.pub botcapp@your-server-ip

# Add the private key content to GitHub secrets as DEPLOY_KEY
cat ~/.ssh/id_rsa
```

### 3. Deploy

Push to the main branch to trigger the deployment:

```bash
git add .
git commit -m "Setup CI/CD pipeline"
git push origin main
```

## 📁 File Structure

```
├── .github/workflows/deploy.yml    # GitHub Actions workflow
├── deploy/
│   ├── deploy.sh                   # Server deployment script
│   ├── server-setup.sh            # Initial server setup
│   └── nginx.conf                 # Nginx configuration
├── Dockerfile                     # Multi-stage Docker build
├── docker-compose.yml             # Local development setup
├── .env.example                   # Environment variables template
└── DEPLOYMENT.md                  # This file
```

## 🔧 Configuration Details

### Docker Configuration

The `Dockerfile` uses a multi-stage build:

1. **Frontend Stage**: Builds React app with Node.js
2. **Backend Stage**: Builds Spring Boot JAR with Maven
3. **Production Stage**: Combines both into minimal JRE image

Key features:
- Layer caching for faster builds
- Non-root user for security
- Health checks included
- Optimized for production

### GitHub Actions Workflow

The pipeline includes:

1. **Test Frontend**: Linting and build verification
2. **Test Backend**: Unit tests and build verification
3. **Build & Push**: Multi-platform Docker image to GHCR
4. **Deploy**: Automated deployment to server

### Server Deployment

Features:
- Zero-downtime deployments
- Database backups before deployment
- Health checks with automatic rollback
- Container restart policies
- Log rotation and cleanup

## 🛠️ Local Development

### Using Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Start with Nginx proxy (production-like)
docker-compose --profile production up --build

# View logs
docker-compose logs -f botc-assistant
```

### Manual Docker Build

```bash
# Build the image
docker build -t botc-assistant .

# Run the container
docker run -d \
  --name botc-assistant \
  -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  botc-assistant
```

## 🔍 Monitoring and Troubleshooting

### Health Checks

The application provides health check endpoints:

```bash
# Application health
curl http://your-server:8080/

# Detailed health (if management endpoints enabled)
curl http://your-server:8080/actuator/health
```

### Logs

```bash
# View application logs
docker logs botc-assistant-container

# View deployment logs
tail -f /opt/botc-assistant/logs/deploy.log

# View system logs
journalctl -u botc-assistant.service -f
```

### Common Issues

#### 1. Deployment Fails

```bash
# Check container status
docker ps -a

# Check container logs
docker logs botc-assistant-container

# Check deployment logs
cat /opt/botc-assistant/logs/deploy.log
```

#### 2. Database Issues

```bash
# Check database file permissions
ls -la /opt/botc-assistant/data/

# Restore from backup
cp /opt/botc-assistant/backups/botc-YYYYMMDD-HHMMSS.db /opt/botc-assistant/data/botc.db
docker restart botc-assistant-container
```

#### 3. Network Issues

```bash
# Check firewall status
sudo ufw status

# Check if port is listening
netstat -tlnp | grep 8080

# Test connectivity
curl -I http://localhost:8080/
```

## 🔒 Security Considerations

### Server Security

- Firewall configured (UFW)
- Fail2ban for intrusion prevention
- Non-root container execution
- Regular security updates

### Application Security

- CORS properly configured
- Rate limiting (via Nginx)
- Static file serving secured
- French landing page optimized

### SSL/HTTPS Setup

For production with SSL:

1. Obtain SSL certificate (Let's Encrypt recommended)
2. Update Nginx configuration
3. Configure environment variables
4. Restart services

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Performance Optimization

### Static Assets

- Optimized React build with Vite
- French content delivery
- Asset caching and compression

### Application

- JVM tuning for production
- Connection pooling optimization
- Static resource caching

### Infrastructure

- Nginx caching and compression
- CDN for static assets (optional)
- Load balancing for multiple instances

## 🔄 Rollback Procedures

### Automatic Rollback

The deployment script includes automatic rollback on health check failure.

### Manual Rollback

```bash
# List available images
docker images | grep botc-assistant

# Rollback to previous version
/opt/botc-assistant/deploy.sh ghcr.io/your-username/your-repo:previous-tag

# Restore database backup
cp /opt/botc-assistant/backups/botc-YYYYMMDD-HHMMSS.db /opt/botc-assistant/data/botc.db
docker restart botc-assistant-container
```

## 📈 Scaling Considerations

### Horizontal Scaling

- Multiple container instances
- Load balancer configuration
- CDN for static assets
- Session-less architecture ready

### Vertical Scaling

- Increase server resources
- JVM memory tuning
- Static asset optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test locally
4. Submit a pull request
5. CI/CD will automatically test and deploy

## 📞 Support

For deployment issues:

1. Check the troubleshooting section
2. Review logs on the server
3. Check GitHub Actions workflow logs
4. Create an issue in the repository

---

**Happy Deploying! 🚀**
