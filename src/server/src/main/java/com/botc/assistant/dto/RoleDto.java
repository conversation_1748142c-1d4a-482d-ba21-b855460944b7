package com.botc.assistant.dto;

import com.botc.assistant.roles.Role;
import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;

/**
 * Data Transfer Object for Role information.
 * Used for API communication to provide a clean interface for role data.
 */
public class RoleDto {
    
    private String id;
    private String name;
    private String description;
    private RoleTeam team;
    private RoleType type;
    private boolean isUnique;
    private boolean hasNightAbility;
    private boolean hasDayAbility;
    private Integer nightPriority;
    private boolean canBeTargeted;
    private String setupInfo;
    
    // Default constructor for JSON serialization
    public RoleDto() {}
    
    // Constructor from Role entity
    public RoleDto(Role role) {
        this.id = role.getId();
        this.name = role.getName();
        this.description = role.getDescription();
        this.team = role.getTeam();
        this.type = role.getType();
        this.isUnique = role.isUnique();
        this.hasNightAbility = role.hasNightAbility();
        this.hasDayAbility = role.hasDayAbility();
        this.nightPriority = role.getNightPriority();
        this.canBeTargeted = role.canBeTargeted();
        this.setupInfo = role.getSetupInfo();
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public RoleTeam getTeam() {
        return team;
    }
    
    public void setTeam(RoleTeam team) {
        this.team = team;
    }
    
    public RoleType getType() {
        return type;
    }
    
    public void setType(RoleType type) {
        this.type = type;
    }
    
    public boolean isUnique() {
        return isUnique;
    }
    
    public void setUnique(boolean unique) {
        isUnique = unique;
    }
    
    public boolean isHasNightAbility() {
        return hasNightAbility;
    }
    
    public void setHasNightAbility(boolean hasNightAbility) {
        this.hasNightAbility = hasNightAbility;
    }
    
    public boolean isHasDayAbility() {
        return hasDayAbility;
    }
    
    public void setHasDayAbility(boolean hasDayAbility) {
        this.hasDayAbility = hasDayAbility;
    }
    
    public Integer getNightPriority() {
        return nightPriority;
    }
    
    public void setNightPriority(Integer nightPriority) {
        this.nightPriority = nightPriority;
    }
    
    public boolean isCanBeTargeted() {
        return canBeTargeted;
    }
    
    public void setCanBeTargeted(boolean canBeTargeted) {
        this.canBeTargeted = canBeTargeted;
    }
    
    public String getSetupInfo() {
        return setupInfo;
    }
    
    public void setSetupInfo(String setupInfo) {
        this.setupInfo = setupInfo;
    }
    
    @Override
    public String toString() {
        return String.format("RoleDto{id='%s', name='%s', type=%s, team=%s}", 
                           id, name, type, team);
    }
}
