package com.botc.assistant.game;

import java.net.Socket;

public class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Runnable {
    private static int clientID = 0;
    private Socket client;
    private String clientName;
    private int gameID;
    private boolean isModerator;

    public ClientHandler(Socket client, String clientName, boolean isModerator) {
        this.client = client;
        this.clientName = clientName;
        this.isModerator = isModerator;
        ++clientID;
    }

    public ClientHandler(Socket client, String clientName, boolean isModerator, int gameID) {
        this(client, clientName, isModerator);
        this.gameID = gameID;
    }

    @Override
    public void run() {

    }
}
