import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Footer from "../components/Footer";
import NavBar from "../components/NavBar";
import { registerUser, getAuthErrorMessage } from '../services/authService';
import { createUserProfile } from '../services/userService';

export default function Register() {
    const [pseudo, setPseudo] = useState('');
    const [password, setPassword] = useState('');
    const [email, setEmail] = useState('');
    const [nom, setNom] = useState('');
    const [prenom, setPrenom] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');
        setSuccess('');

        try {
            // Validate inputs
            if (!pseudo.trim() || !password.trim() || !email.trim() || !nom.trim() || !prenom.trim()) {
                throw new Error('Veuillez remplir tous les champs.');
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email.trim())) {
                throw new Error('Veuillez entrer une adresse email valide.');
            }

            // Validate password length
            if (password.length < 6) {
                throw new Error('Le mot de passe doit contenir au moins 6 caractères.');
            }

            // Register user with Firebase Auth
            const userCredential = await registerUser(email.trim(), password, pseudo.trim());

            // Create user profile in Firestore
            await createUserProfile(userCredential.user.uid, {
                email: email.trim(),
                pseudo: pseudo.trim(),
                prenom: prenom.trim(),
                nom: nom.trim(),
                isNarrator: false // Default to false, can be changed later
            });

            setSuccess('Compte créé avec succès ! Redirection...');

            // Redirect to login page after a short delay
            setTimeout(() => {
                navigate('/login');
            }, 2000);

        } catch (err) {
            console.error('Registration error:', err);
            if (err.code) {
                setError(getAuthErrorMessage(err.code));
            } else {
                setError(err.message || 'Une erreur inattendue s\'est produite.');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <NavBar />
            <main id="register-page">
                <section id="register">
                    <div className="register-card">
                        <h1>Créer un compte</h1>

                        {error && (
                            <div className="error-message">
                                {error}
                            </div>
                        )}

                        {success && (
                            <div className="success-message">
                                {success}
                            </div>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label htmlFor="prenom">Prénom</label>
                                <input
                                    type="text"
                                    id="prenom"
                                    name="prenom"
                                    placeholder="Prénom"
                                    value={prenom}
                                    onChange={(e) => setPrenom(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="nom">Nom</label>
                                <input
                                    type="text"
                                    id="nom"
                                    name="nom"
                                    placeholder="Nom"
                                    value={nom}
                                    onChange={(e) => setNom(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="email">Email</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    placeholder="Email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="pseudo">Pseudo</label>
                                <input
                                    type="text"
                                    id="pseudo"
                                    name="pseudo"
                                    placeholder="Pseudo"
                                    value={pseudo}
                                    onChange={(e) => setPseudo(e.target.value)}
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="password">Mot de passe</label>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    placeholder="Mot de passe (min. 6 caractères)"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    disabled={loading}
                                    required
                                    minLength={6}
                                />
                            </div>

                            <button type="submit" className="confirm-button" disabled={loading}>
                                {loading ? 'Création du compte...' : 'Confirmer'}
                            </button>
                            <Link to="/login" className="create-account-link">
                                Se connecter
                            </Link>
                        </form>
                    </div>
                </section>
            </main>
            <Footer />
        </>
    );
}
