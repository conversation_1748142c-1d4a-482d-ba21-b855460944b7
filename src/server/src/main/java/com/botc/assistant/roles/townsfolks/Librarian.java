package com.botc.assistant.roles.townsfolks;

/**
 * <PERSON><PERSON>rian - Townsfolk role
 * 
 * You start knowing that 1 of 2 players is a particular Outsider. (Or that zero Outsiders are in play.)
 * 
 * The Librarian learns information about Outsiders on the first night.
 * They learn that one of two specific players is a particular type of Outsider,
 * or they learn that there are no Outsiders in play.
 */
public class <PERSON><PERSON>rian extends TownsFolk {
    
    public static final String ROLE_ID = "librarian";
    public static final String ROLE_NAME = "Librarian";
    public static final String ROLE_DESCRIPTION = "You start knowing that 1 of 2 players is a particular Outsider. (Or that zero Outsiders are in play.)";
    
    private Librarian(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Librarian create() {
        return builder().build();
    }
    
    /**
     * Builder for Librarian role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(true);
            nightPriority(3); // First night information
            setupInfo("Learns that 1 of 2 players is a particular Outsider, or that zero Outsiders are in play");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Li<PERSON>rian build() {
            return new <PERSON><PERSON>rian(this);
        }
    }
}
