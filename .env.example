# Environment Configuration for Todo App

# Database Configuration
SPRING_DATASOURCE_URL=*****************************
SPRING_JPA_HIBERNATE_DDL_AUTO=update

# Server Configuration
SERVER_PORT=8080
SPRING_PROFILES_ACTIVE=production

# Logging Configuration
LOGGING_LEVEL_ORG_HIBERNATE=WARN
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK=INFO

# Security Configuration (for production)
# SPRING_SECURITY_USER_NAME=admin
# SPRING_SECURITY_USER_PASSWORD=your-secure-password

# SSL Configuration (for production with HTTPS)
# SERVER_SSL_ENABLED=true
# SERVER_SSL_KEY_STORE=/app/ssl/keystore.p12
# SERVER_SSL_KEY_STORE_PASSWORD=your-keystore-password
# SERVER_SSL_KEY_STORE_TYPE=PKCS12

# CORS Configuration (if needed for specific domains)
# CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Health Check Configuration
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when-authorized
