#!/bin/bash

# Server Setup Script for Blood on the Clocktower Assistant Deployment
# This script prepares a Ubuntu/Debian server for automated deployments

set -e

# Configuration
APP_USER="botcassistant"
APP_DIR="/opt/botc-assistant"
# Dynamically fetch the latest Docker Compose version from GitHub
DOCKER_COMPOSE_VERSION="$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep -Po '"tag_name": "\K.*?(?=")')"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "Please run this script as root (use sudo)"
    exit 1
fi

log "Starting server setup for Blood on the Clocktower Assistant deployment..."

# Update system packages
log "Updating system packages..."
apt-get update
apt-get upgrade -y

# Install required packages
log "Installing required packages..."
apt-get install -y \
    curl \
    wget \
    git \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    ufw \
    fail2ban \
    logrotate

# Install Docker
log "Installing Docker..."
if ! command -v docker &> /dev/null; then
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    log "Docker installed successfully"
else
    log "Docker is already installed"
fi

# Install Docker Compose (standalone)
log "Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    log "Docker Compose installed successfully"
else
    log "Docker Compose is already installed"
fi

# Create application user
log "Creating application user: $APP_USER"
if ! id "$APP_USER" &>/dev/null; then
    useradd -r -s /bin/bash -d "$APP_DIR" -m "$APP_USER"
    usermod -aG docker "$APP_USER"
    log "User $APP_USER created successfully"
else
    log "User $APP_USER already exists"
fi

# Create application directories
log "Creating application directories..."
mkdir -p "$APP_DIR"/{data,backups,logs,deploy}
chown -R "$APP_USER:$APP_USER" "$APP_DIR"

# Setup firewall
log "Configuring firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8080/tcp  # For direct access during setup
ufw --force enable

# Configure fail2ban
log "Configuring fail2ban..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF

systemctl restart fail2ban
systemctl enable fail2ban

# Setup log rotation
log "Configuring log rotation..."
cat > /etc/logrotate.d/botc-assistant << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        docker kill -s USR1 botc-assistant-container 2>/dev/null || true
    endscript
}
EOF

# Create deployment script
log "Creating deployment script..."
cat > "$APP_DIR/deploy.sh" << 'EOF'
#!/bin/bash

# Blood on the Clocktower Assistant Deployment Script
# This script handles zero-downtime deployment with health checks and rollback

set -e

# Configuration
APP_NAME="botc-assistant"
CONTAINER_NAME="${APP_NAME}-container"
NETWORK_NAME="${APP_NAME}-network"
DATA_DIR="/opt/botc-assistant/data"
BACKUP_DIR="/opt/botc-assistant/backups"
LOG_FILE="/opt/botc-assistant/logs/deploy.log"
HEALTH_CHECK_URL="http://localhost:8080/"
MAX_HEALTH_CHECKS=30
HEALTH_CHECK_INTERVAL=2

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

# Check if image parameter is provided
if [ -z "$1" ]; then
    error "Usage: $0 <docker-image>"
    exit 1
fi

NEW_IMAGE="$1"

log "Starting deployment of $NEW_IMAGE"

# Create necessary directories
mkdir -p "$DATA_DIR" "$BACKUP_DIR" "$(dirname "$LOG_FILE")"

# Create Docker network if it doesn't exist
if ! docker network ls | grep -q "$NETWORK_NAME"; then
    log "Creating Docker network: $NETWORK_NAME"
    docker network create "$NETWORK_NAME"
fi

# Backup current database if it exists
if [ -f "$DATA_DIR/botc.db" ]; then
    BACKUP_FILE="$BACKUP_DIR/botc-$(date +%Y%m%d-%H%M%S).db"
    log "Backing up database to $BACKUP_FILE"
    cp "$DATA_DIR/botc.db" "$BACKUP_FILE"

    # Keep only last 10 backups
    ls -t "$BACKUP_DIR"/botc-*.db | tail -n +11 | xargs -r rm
fi

# Pull the new image
log "Pulling new image: $NEW_IMAGE"
if ! docker pull "$NEW_IMAGE"; then
    error "Failed to pull image: $NEW_IMAGE"
    exit 1
fi

# Stop and remove old container
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    log "Stopping and removing old container"
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# Start new container
log "Starting new container"
docker run -d \
    --name "$CONTAINER_NAME" \
    --network "$NETWORK_NAME" \
    -p 8080:8080 \
    -v "$DATA_DIR:/app/data" \
    -e SPRING_PROFILES_ACTIVE=production \
    -e SPRING_DATASOURCE_URL=***************************** \
    --restart unless-stopped \
    "$NEW_IMAGE"

# Health check
log "Performing health checks..."
for i in $(seq 1 $MAX_HEALTH_CHECKS); do
    log "Health check attempt $i/$MAX_HEALTH_CHECKS"
    
    if curl -f -s "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
        log "Health check passed!"
        break
    fi
    
    if [ $i -eq $MAX_HEALTH_CHECKS ]; then
        error "Health check failed after $MAX_HEALTH_CHECKS attempts"
        exit 1
    fi
    
    sleep $HEALTH_CHECK_INTERVAL
done

# Cleanup old images (keep last 3)
log "Cleaning up old images..."
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.ID}}" | \
    grep "$(echo "$NEW_IMAGE" | cut -d: -f1)" | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi 2>/dev/null || true

log "Deployment completed successfully!"
log "Application is running at http://localhost:8080"
EOF

chmod +x "$APP_DIR/deploy.sh"
chown "$APP_USER:$APP_USER" "$APP_DIR/deploy.sh"

# Create systemd service for auto-start
log "Creating systemd service..."
cat > /etc/systemd/system/botc-assistant.service << EOF
[Unit]
Description=Blood on the Clocktower Assistant Container
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
User=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/docker start botc-assistant-container
ExecStop=/usr/bin/docker stop botc-assistant-container
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable botc-assistant.service

# Configure SSH for secure key-based authentication
log "Configuring SSH server for secure authentication..."
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# Ensure SSH is configured for key-based authentication
cat >> /etc/ssh/sshd_config.d/99-botc-deployment.conf << EOF
# Configuration for BotC Assistant deployment
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PasswordAuthentication yes
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes
EOF

# Restart SSH service to apply changes
systemctl restart sshd

# Setup SSH key for GitHub Actions (if provided)
if [ -n "$2" ]; then
    log "Setting up SSH key for deployments..."
    mkdir -p "/home/<USER>/.ssh"
    echo "$2" > "/home/<USER>/.ssh/authorized_keys"
    chmod 600 "/home/<USER>/.ssh/authorized_keys"
    chmod 700 "/home/<USER>/.ssh"
    chown -R "$APP_USER:$APP_USER" "/home/<USER>/.ssh"

    # Test SSH key setup
    log "Testing SSH key setup..."
    if sudo -u "$APP_USER" ssh-keygen -l -f "/home/<USER>/.ssh/authorized_keys" >/dev/null 2>&1; then
        log "SSH key setup successful"
    else
        warning "SSH key format may be invalid"
    fi
fi

log "Server setup completed successfully!"
info "Next steps:"
info "1. Configure your GitHub repository secrets:"
info "   - DEPLOY_HOST: Your server IP address"
info "   - DEPLOY_USER: $APP_USER"
info "   - DEPLOY_KEY: Your private SSH key (without passphrase)"
info "   - DEPLOY_PORT: 22 (or your custom SSH port)"
info ""
info "2. Add your SSH public key to the server:"
info "   ssh-copy-id -i ~/.ssh/your_key.pub $APP_USER@your-server-ip"
info ""
info "3. Test SSH connection:"
info "   ssh -i ~/.ssh/your_private_key $APP_USER@your-server-ip"
info ""
info "4. Push your code to the main branch to trigger deployment"
info ""
info "5. Access your application at: http://your-server-ip:8080"
info ""
info "Application directory: $APP_DIR"
info "Deployment script: $APP_DIR/deploy.sh"
info "Logs directory: $APP_DIR/logs"
info "Data directory: $APP_DIR/data"
info ""
info "Troubleshooting SSH issues:"
info "- Check SSH logs: sudo tail -f /var/log/auth.log"
info "- Verify SSH config: sudo sshd -T | grep -E '(PubkeyAuthentication|AuthorizedKeysFile)'"
info "- Test key format: ssh-keygen -l -f ~/.ssh/authorized_keys"
EOF
