package com.botc.assistant.game;

import com.botc.assistant.model.Player;
import com.botc.assistant.roles.Role;
import java.net.Socket;
import java.util.List;

public class Moderator extends ClientHandler {

    public Moderator(Socket client, String clientName, boolean isModerator) {
        super(client, clientName, isModerator);
    }

    public void assignRoles(List<Role> roles, List<Player> players) {
        /* TODO */
    }

    public void randomAssignRoles(List<Role> roles, List<Player> players) {
        /* TODO */
    }

    public void createGame(int maxPlayers) {
        /* TODO */
    }

    public void runNightPhase() {
        /* TODO */
    }

    public void runDayPhase() {
        /* TODO */
    }

    public void startVote(int playerPos) {
        /* TODO */
    }

}
