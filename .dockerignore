# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
DEPLOYMENT.md
CI-CD-SUMMARY.md
*.md
Livrables/

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories (exclude from build context - will be installed in container)
client/node_modules/
server/target/
jspm_packages/

# Optional npm cache directory
.npm
.yarn
.pnpm-store

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env*
!.env.example

# Build outputs (exclude from build context)
client/dist/
client/build/
server/target/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/

# Docker (exclude other Docker files)
Dockerfile.*
docker-compose*.yml
.dockerignore

# CI/CD (exclude deployment scripts from build context)
.github/
deploy/
scripts/

# Development tools
.editorconfig
.prettierrc
.eslintrc*

# Test files (exclude from production build)
**/*.test.js
**/*.test.jsx
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.jsx
**/*.spec.ts
**/*.spec.tsx
test/
tests/
__tests__/

# Maven specific (keep .mvn directory but exclude wrapper jar)
server/.mvn/wrapper/maven-wrapper.jar
server/.mvn/wrapper/maven-wrapper.properties

# Gradle specific (if used)
.gradle/
build/

# IntelliJ IDEA
*.iml
*.ipr
*.iws

# Eclipse
.project
.classpath
.settings/

# NetBeans
nbproject/

# Visual Studio Code
.vscode/

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local development
local/
dev/

# Additional optimizations for smaller build context
*.zip
*.tar.gz
*.rar
*.7z
.cache/
.npm-cache/
.yarn-cache/
