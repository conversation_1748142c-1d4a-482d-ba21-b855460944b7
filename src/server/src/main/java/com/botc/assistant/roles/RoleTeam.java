package com.botc.assistant.roles;

/**
 * Enumeration of the different teams in Blood on the Clock Tower.
 */
public enum RoleTeam {
    /**
     * Good team - wins when all evil players are eliminated
     */
    GOOD("Good"),
    
    /**
     * Evil team - wins when good players equal or are outnumbered by evil players
     */
    EVIL("Evil");
    
    private final String displayName;
    
    RoleTeam(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
