# Firebase Setup Guide

This guide will help you set up Firebase Authentication and Firestore for the Blood on the Clocktower Assistant application.

## Prerequisites

1. A Google account
2. Access to the [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "botc-assistant")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project, go to "Authentication" in the left sidebar
2. Click "Get started"
3. Go to the "Sign-in method" tab
4. Enable "Email/Password" authentication
5. Save the changes

## Step 3: Create Firestore Database

1. In your Firebase project, go to "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" (we'll update security rules later)
4. Select a location for your database
5. Click "Done"

## Step 4: Update Firestore Security Rules

**IMPORTANT**: You need to update the Firestore security rules to allow the application to work properly.

1. In Firestore Database, go to the "Rules" tab
2. Replace the default rules with the content from `firestore.rules` file
3. Click "Publish" to save the rules

## Step 5: Get Firebase Configuration

1. Go to "Project settings" (gear icon in the left sidebar)
2. Scroll down to "Your apps" section
3. Click the web icon (`</>`) to add a web app
4. Enter an app nickname (e.g., "botc-assistant-web")
5. Click "Register app"
6. Copy the Firebase configuration object

## Step 6: Configure Environment Variables

1. Open the `.env` file in the `src/client` directory
2. Replace the placeholder values with your actual Firebase configuration

## Step 7: Test the Setup

1. Start the development server: `npm run dev`
2. Navigate to the register page
3. Create a test account
4. Check that the user appears in Firebase Authentication
5. Check that the user profile is created in Firestore
6. Test login functionality with the created account

## Troubleshooting

- **"Missing or insufficient permissions"**: Make sure you've updated the Firestore security rules as described in Step 4
- **"Firebase configuration not found"**: Make sure your `.env` file has the correct variable names (prefixed with `VITE_`)
- **"Network error"**: Ensure your Firebase project is active and the configuration is correct
