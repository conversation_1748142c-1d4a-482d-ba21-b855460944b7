// Authentication context for managing auth state across the application
import { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChange } from '../services/authService';
import { getUserProfile } from '../services/userService';

// Create the authentication context
const AuthContext = createContext({});

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Authentication provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Listen for authentication state changes
    const unsubscribe = onAuthStateChange(async (user) => {
      try {
        setError(null);
        
        if (user) {
          // User is signed in
          setCurrentUser(user);
          
          // Fetch user profile from Firestore
          const profile = await getUserProfile(user.uid);
          setUserProfile(profile);
        } else {
          // User is signed out
          setCurrentUser(null);
          setUserProfile(null);
        }
      } catch (err) {
        console.error('Error in auth state change:', err);
        setError('Erreur lors de la récupération des données utilisateur');
        setCurrentUser(null);
        setUserProfile(null);
      } finally {
        setLoading(false);
      }
    });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);

  // Function to update user profile in context
  const updateUserProfile = (newProfile) => {
    setUserProfile(newProfile);
  };

  // Function to clear error
  const clearError = () => {
    setError(null);
  };

  // Context value
  const value = {
    currentUser,
    userProfile,
    loading,
    error,
    updateUserProfile,
    clearError,
    // Computed properties
    isAuthenticated: !!currentUser,
    isNarrator: userProfile?.isNarrator || false,
    displayName: userProfile?.pseudo || currentUser?.displayName || 'Utilisateur'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
