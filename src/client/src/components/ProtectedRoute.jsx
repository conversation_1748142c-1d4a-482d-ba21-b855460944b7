// Protected route component that requires authentication
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

export default function ProtectedRoute({ children }) {
    const { isAuthenticated, loading } = useAuth();

    // Show loading while checking authentication status
    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-spinner">Chargement...</div>
            </div>
        );
    }

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

    // Render the protected component if authenticated
    return children;
}
