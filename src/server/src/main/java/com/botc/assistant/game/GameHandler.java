package com.botc.assistant.game;

import com.botc.assistant.model.Player;
import com.botc.assistant.roles.Role;
import java.util.List;
import java.util.Map;

public class GameHandler {
    private int gameID;
    private Moderator moderator;
    private Player[] players;
    List<Role> activeRoles;
    private int dayCount;
    private int maxPlayer;
    private Map<Role, Integer> maxRolesCount;

    public GameHandler(/* TODO */) {
        /* TODO */
    }

    private Map<Role, Integer> computeMaxRolesCount() {
        /* TODO */
        return null;
    }

    public void startGame() {
        /* TODO */
    }

    public void nextPhase() {
        /* TODO */
    }

    public boolean isGameOver() {
        /* TODO */
        return false;
    }

    public void startVoteSeq(int playerPos) {
        /* TODO */
    }

    public List<Player> getLivingPlayers() {
        /* TODO */
        return null;
    }
}
