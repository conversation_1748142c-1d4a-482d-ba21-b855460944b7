/* Blood on the Clocktower Assistant Styles */

.app {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    width: 100%;
    align-items: center;
    z-index: 1;
    position: relative;
}

.hero-text {
    text-align: left;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #a78bfa 50%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    display: block;
    font-size: 2.5rem;
    font-weight: 600;
    margin-top: 0.5rem;
    color: #fbbf24;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #e5e7eb;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.6);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #8b5cf6;
}

.btn-secondary:hover {
    background: #8b5cf6;
    transform: translateY(-2px);
}

/* Hero Visual - Clocktower */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.clocktower-icon {
    width: 300px;
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clock-face {
    width: 200px;
    height: 200px;
    border: 8px solid #8b5cf6;
    border-radius: 50%;
    position: relative;
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5),
        inset 0 0 20px rgba(139, 92, 246, 0.2);
    animation: clockGlow 3s ease-in-out infinite alternate;
}

.clock-hand {
    position: absolute;
    background: #fbbf24;
    border-radius: 2px;
    transform-origin: bottom center;
    left: 50%;
    bottom: 50%;
}

.hour-hand {
    width: 4px;
    height: 60px;
    margin-left: -2px;
    transform: translateX(-50%) rotate(45deg);
    animation: hourRotate 12s linear infinite;
}

.minute-hand {
    width: 2px;
    height: 80px;
    margin-left: -1px;
    transform: translateX(-50%) rotate(90deg);
    animation: minuteRotate 1s linear infinite;
}

.clock-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #fbbf24;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

@keyframes clockGlow {
    0% {
        box-shadow: 0 0 30px rgba(139, 92, 246, 0.5), inset 0 0 20px rgba(139, 92, 246, 0.2);
    }

    100% {
        box-shadow: 0 0 50px rgba(139, 92, 246, 0.8), inset 0 0 30px rgba(139, 92, 246, 0.4);
    }
}

@keyframes hourRotate {
    0% {
        transform: translateX(-50%) rotate(45deg);
    }

    100% {
        transform: translateX(-50%) rotate(405deg);
    }
}

@keyframes minuteRotate {
    0% {
        transform: translateX(-50%) rotate(90deg);
    }

    100% {
        transform: translateX(-50%) rotate(450deg);
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Styles */
.about-section,
.features-section {
    padding: 6rem 0;
}

.about-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #a78bfa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Grid */
.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.about-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.about-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fbbf24;
}

.about-card p {
    color: #e5e7eb;
    line-height: 1.6;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 3rem;
}

.feature-card {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-3px);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #a78bfa;
}

.feature-card p {
    color: #d1d5db;
    line-height: 1.5;
    margin: 0;
}

/* Footer */
.app-footer {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.app-footer p {
    margin: 0.5rem 0;
    color: #d1d5db;
}

.footer-note {
    font-size: 0.9rem;
    color: #9ca3af;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-section,
    .features-section {
        padding: 4rem 0;
    }

    .container {
        padding: 0 1rem;
    }

    .clocktower-icon {
        width: 200px;
        height: 200px;
    }

    .clock-face {
        width: 150px;
        height: 150px;
    }
}
