package com.botc.assistant.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for Game State information.
 * Represents the current state of a Blood on the Clock Tower game.
 */
public class GameStateDto {
    
    private String gameId;
    private String gameName;
    private GamePhase phase;
    private int dayNumber;
    private int playerCount;
    private List<PlayerDto> players;
    private List<String> availableRoles;
    private String currentPlayer; // Player whose turn it is
    private boolean isNightPhase;
    private boolean isDayPhase;
    private boolean isVotingPhase;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String storytellerNotes;
    
    // Default constructor
    public GameStateDto() {}
    
    // Constructor with basic info
    public GameStateDto(String gameId, String gameName, int playerCount) {
        this.gameId = gameId;
        this.gameName = gameName;
        this.playerCount = playerCount;
        this.phase = GamePhase.SETUP;
        this.dayNumber = 0;
        this.isNightPhase = false;
        this.isDayPhase = false;
        this.isVotingPhase = false;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }
    
    // Enum for game phases
    public enum GamePhase {
        SETUP,
        FIRST_NIGHT,
        DAY,
        NIGHT,
        VOTING,
        FINISHED
    }
    
    // Getters and Setters
    public String getGameId() {
        return gameId;
    }
    
    public void setGameId(String gameId) {
        this.gameId = gameId;
    }
    
    public String getGameName() {
        return gameName;
    }
    
    public void setGameName(String gameName) {
        this.gameName = gameName;
    }
    
    public GamePhase getPhase() {
        return phase;
    }
    
    public void setPhase(GamePhase phase) {
        this.phase = phase;
    }
    
    public int getDayNumber() {
        return dayNumber;
    }
    
    public void setDayNumber(int dayNumber) {
        this.dayNumber = dayNumber;
    }
    
    public int getPlayerCount() {
        return playerCount;
    }
    
    public void setPlayerCount(int playerCount) {
        this.playerCount = playerCount;
    }
    
    public List<PlayerDto> getPlayers() {
        return players;
    }
    
    public void setPlayers(List<PlayerDto> players) {
        this.players = players;
        if (players != null) {
            this.playerCount = players.size();
        }
    }
    
    public List<String> getAvailableRoles() {
        return availableRoles;
    }
    
    public void setAvailableRoles(List<String> availableRoles) {
        this.availableRoles = availableRoles;
    }
    
    public String getCurrentPlayer() {
        return currentPlayer;
    }
    
    public void setCurrentPlayer(String currentPlayer) {
        this.currentPlayer = currentPlayer;
    }
    
    public boolean isNightPhase() {
        return isNightPhase;
    }
    
    public void setNightPhase(boolean nightPhase) {
        isNightPhase = nightPhase;
    }
    
    public boolean isDayPhase() {
        return isDayPhase;
    }
    
    public void setDayPhase(boolean dayPhase) {
        isDayPhase = dayPhase;
    }
    
    public boolean isVotingPhase() {
        return isVotingPhase;
    }
    
    public void setVotingPhase(boolean votingPhase) {
        isVotingPhase = votingPhase;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    public String getStorytellerNotes() {
        return storytellerNotes;
    }
    
    public void setStorytellerNotes(String storytellerNotes) {
        this.storytellerNotes = storytellerNotes;
    }
    
    @Override
    public String toString() {
        return String.format("GameStateDto{gameId='%s', gameName='%s', phase=%s, dayNumber=%d, playerCount=%d}", 
                           gameId, gameName, phase, dayNumber, playerCount);
    }
}
