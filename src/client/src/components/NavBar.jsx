import MenuBurger from "./Burger";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

export default function NavBar() {
    const { isAuthenticated, isNarrator, loading } = useAuth();

    return (
        <>
            <header>
                <div>
                    <Link to="/">
                        <picture>
                            <source media="(min-width: 1000px)" srcSet="/src/assets/logo-row.png" />
                            <img src="/src/assets/logo-column.png" alt="Responsive" />
                        </picture>
                    </Link>
                </div>
                <div>
                    <MenuBurger
                        status={isAuthenticated}
                        mj={isNarrator}
                        loading={loading}
                    />
                </div>
            </header>
        </>
    );
}
