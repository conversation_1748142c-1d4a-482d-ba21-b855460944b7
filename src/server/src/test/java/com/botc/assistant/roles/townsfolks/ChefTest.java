package com.botc.assistant.roles.townsfolks;

import com.botc.assistant.roles.RoleTeam;
import com.botc.assistant.roles.RoleType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for the Chef role.
 */
class ChefTest {
    
    @Test
    void testChefCreation() {
        Chef chef = Chef.create();
        
        assertNotNull(chef);
        assertEquals("chef", chef.getId());
        assertEquals("Chef", chef.getName());
        assertEquals(RoleTeam.GOOD, chef.getTeam());
        assertEquals(RoleType.TOWNSFOLK, chef.getType());
    }
    
    @Test
    void testChefProperties() {
        Chef chef = Chef.create();
        
        assertTrue(chef.isUnique());
        assertTrue(chef.hasNightAbility());
        assertFalse(chef.hasDayAbility());
        assertTrue(chef.canBeTargeted());
        assertEquals(Integer.valueOf(1), chef.getNightPriority());
    }
    
    @Test
    void testChefDescription() {
        Chef chef = Chef.create();
        
        assertNotNull(chef.getDescription());
        assertTrue(chef.getDescription().contains("pairs of evil players"));
    }
    
    @Test
    void testChefSetupInfo() {
        Chef chef = Chef.create();
        
        assertNotNull(chef.getSetupInfo());
        assertTrue(chef.getSetupInfo().contains("pairs of evil players"));
    }
    
    @Test
    void testChefBuilder() {
        Chef chef = Chef.builder()
                .name("Custom Chef")
                .description("Custom description")
                .build();
        
        assertEquals("chef", chef.getId()); // ID should remain constant
        assertEquals("Custom Chef", chef.getName());
        assertEquals("Custom description", chef.getDescription());
        assertEquals(RoleTeam.GOOD, chef.getTeam());
        assertEquals(RoleType.TOWNSFOLK, chef.getType());
    }
    
    @Test
    void testChefEquality() {
        Chef chef1 = Chef.create();
        Chef chef2 = Chef.create();
        
        assertEquals(chef1, chef2);
        assertEquals(chef1.hashCode(), chef2.hashCode());
    }
    
    @Test
    void testChefToString() {
        Chef chef = Chef.create();
        String toString = chef.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("Chef"));
        assertTrue(toString.contains("Townsfolk"));
    }
}
