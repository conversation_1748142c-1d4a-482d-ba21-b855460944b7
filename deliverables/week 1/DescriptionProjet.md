# Description du projet de PDG - BloodOnTheClockTower-Assistant

<PERSON><PERSON>at <PERSON>

## Objectifs

L’objectif du projet est de virtualiser le jeu de société [**Blood of the ClockTower**](https://bloodontheclocktower.com/) afin de supprimer la dépendance aux éléments physiques (cartes, jetons, etc.) et d’offrir au Maître du Jeu (MJ) une vue globale et interactive de la partie.

L'application offre donc une plateforme interactive qui :

- simplifie la gestion des parties pour le Maître du Jeu (MJ),

- permet aux joueurs d’interagir via une interface mobile ou web

- conserve l’esprit social, stratégique et narratif du jeu original.

L’application doit ainsi permettre au MJ de gérer efficacement l’état du jeu (joueurs vivants/morts, effets de rôles, phases jour/nuit, votes, etc.) tout en offrant aux joueurs une expérience fluide et immersive.

## Contexte

**Blood on the Clocktower** est un jeu de rôle social dans lequel chaque joueur incarne un personnage doté de pouvoirs spécifiques. Les règles du jeu se situent [ici](https://bloodontheclocktower.com/).

La partie se déroule en plusieurs phases (jour et nuit) et oppose les villageois, qui doivent démasquer et éliminer le démon, aux forces du mal qui cherchent à semer la confusion et à survivre jusqu’au bout.
Les joueurs doivent appliquer leurs capacités pour survivre et gagner la partie.

Dans sa version physique, le jeu demande une grande attention et une gestion manuelle des rôles, des pouvoirs et des états du jeu. L’application permettra de :

- Réduire la charge cognitive du MJ (gestion automatisée d’événements).

- Fluidifier l’expérience des joueurs (notifications de rôles, rappel des actions possibles, suivi en direct).

  -Faciliter la mise en place de parties à distance ou hybrides.

## Exigences Fonctionnels

L'application doit s'assurer du bon fonctionnement d'une partie du jeu **Blood on the ClockTower**.

| Fonctionnalités générales                                                                                 |                 |
| :-------------------------------------------------------------------------------------------------------- | :-------------: |
| Création et gestion de comptes utilisateurs (MJ + joueurs)                                                |    must-have    |
| Connexion sécurisée                                                                                       |    must-have    |
| Génération de codes uniques pour rejoindre une partie                                                     |    must-have    |
| Gestion de plusieurs parties en parallèle                                                                 |    must-have    |
| Système de notifications (push/visuelles) pour informer les joueurs des actions et changements d’état     |  nice-to-have   |
| Historique des événements (journal de partie pour le MJ)                                                  |  nice-to-have   |
| **Côté Joueur**                                                                                           |                 |
| Rejoindre une partie via un code                                                                          |    must-have    |
| Découvrir son rôle en début de partie (affichage secret et individuel)                                    |    must-have    |
| Consulter à tout moment la description de son rôle et ses pouvoirs                                        |    must-have    |
| Être notifié des moments où son rôle peut être activé (pendant la nuit, pendant un vote, etc.)            |  nice-to-have   |
| Pouvoir exécuter des actions spécifiques à son rôle via son interface                                     |    must-have    |
| Participer aux votes (exécution, condamnation, décisions collectives)                                     |    must-have    |
| Visualiser les informations publiques (qui est mort, changements de phase, résultats des votes)           |    must-have    |
| Interface adaptée aux mobiles (simplicité, clarté, ergonomie)                                             |    must-have    |
| **Côté Maître du Jeu (MJ)**                                                                               |                 |
| Créer une nouvelle partie après authentification                                                          |    must-have    |
| Définir le nombre de joueurs et assigner automatiquement ou manuellement les rôles                        |    must-have    |
| Accéder à une vue globale en temps réel (état des joueurs, rôles, vivants/morts, historique des actions)  |    must-have    |
| Contrôler les phases du jeu (jour/nuit, votes, exécutions)                                                |    must-have    |
| Déclencher les pouvoirs des rôles au bon moment et valider les actions proposées par les joueurs          |    must-have    |
| Gérer les votes : ouverture/fermeture                                                                     |    must-have    |
| Pouvoir intervenir manuellement (correction d’erreurs, ajout/suppression de joueurs, modification d’état) |  nice-to-have   |
| Disposer d’un tableau de bord ergonomique regroupant toutes les informations utiles                       |    must-have    |
| Exporter l’historique de la partie                                                                        | if we have time |

## Requirements non-fonctionnels

| Expérience utilisateur                                                                                                      |                 |
| :-------------------------------------------------------------------------------------------------------------------------- | :-------------: |
| Interfaces intuitives, adaptées aussi bien aux nouveaux joueurs qu’aux habitués.                                            |    must-have    |
| Navigation fluide, sans surcharge d’informations                                                                            |    must-have    |
| Accessibilité : compatibilité mobile (responsive), contrastes respectés, lisibilité optimisée                               |    must-have    |
| Utilisation de couleurs, icônes et animations pour indiquer les changements d’état (jour/nuit, mort/vivant, votes en cours) |  nice-to-have   |
| **Performance et scalabilité**                                                                                              |                 |
| Support jusqu’à 30 joueurs connectés simultanément à une même partie sans ralentissements                                   |    must-have    |
| Support de plusieurs parties hébergées en parallèle                                                                         |  nive-to-have   |
| Temps de réponse de l’application < 200 ms pour les actions courantes (votes, changements d’état, notifications)            |  nice-to-have   |
| Optimisation de la consommation réseau pour les connexions mobiles                                                          |  nice-to-have   |
| **Sécurité**                                                                                                                |                 |
| Authentification sécurisée du MJ (login + mot de passe)                                                                     |    must-have    |
| Gestion des sessions joueurs via tokens sécurisés                                                                           |    must-have    |
| Génération de codes de partie aléatoires, robustes contre les collisions et brute-force                                     |  nice-to-have   |
| Communication chiffrée (HTTPS)                                                                                              |  nice-to-have   |
| Protection contre la fraude (exemple : un joueur ne peut pas consulter les rôles des autres)                                |  nice-to-have   |
| **Fiabilité**                                                                                                               |
| Sauvegarde automatique et régulière de l’état de la partie                                                                  |  nice-to-have   |
| Restauration possible en cas de déconnexion du MJ ou d’un joueur                                                            |  nice-to-have   |
| **Maintenance et évolutivité**                                                                                              |                 |
| Architecture modulaire permettant l’ajout futur de nouvelles extensions de règles ou variantes de jeu                       |    must-have    |
| Documentation claire pour les utilisateurs (guide MJ, guide joueurs)                                                        |    must-have    |
| Support multilingue prévu (FR/EN au minimum)                                                                                | if we have time |

## Extensions possibles

- Mode “Spectateur” permettant à des personnes de suivre la partie sans interagir

- Outils de statistiques de partie (nombre de votes, rôles les plus joués, durée moyenne, etc.)

- Intégration audio/vidéo/chat pour les parties à distance

- Mode tutoriel interactif pour les nouveaux joueurs

- Intelligence artificielle optionnelle pour assister le MJ dans certaines décisions
