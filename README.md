# PDG Project 2025

### Authors :

Berberat Alex  
<PERSON>rat Lisa  
<PERSON>

# Blood on the Clocktower Assistant

A beautiful, responsive landing page for the Blood on the Clocktower Assistant application. Built with Spring Boot backend and React frontend, this application serves as the entry point for the ultimate social deduction game companion.

## 🎭 About Blood on the Clocktower

Blood on the Clocktower is a social deduction game where good must find and execute the demon before evil takes control. Set in the mysterious town of Ravenswood Bluff, players use their unique abilities to gather information, deduce the truth, and survive the night.

## 🎭 Landing Page

http://www.lesidiotsduvillage.ch:8080/

## 🚀 Features

### Frontend (React + Vite)

- ✅ **Beautiful Landing Page** - Stunning, responsive design with animated clocktower
- ✅ **Modern React Components** - Built with functional components and hooks
- ✅ **Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices
- ✅ **Gradient Animations** - Eye-catching visual effects and smooth transitions
- ✅ **Game Information** - Clear explanation of Blood on the Clocktower mechanics
- ✅ **Feature Showcase** - Highlights of planned assistant features

### Backend (Spring Boot)

- ✅ **Static Resource Serving** - Optimized delivery of frontend assets
- ✅ **Production Ready** - Configured for deployment with proper caching
- ✅ **Health Monitoring** - Application health checks and monitoring
- ✅ **CORS Configuration** - Properly configured for frontend communication

## 🏗️ Project Structure

```
BloodOnTheClockTower-Assistant/
├── README.md                           # Documentation principale du projet
├── DEPLOYMENT.md                       # Instructions de déploiement
├── Dockerfile                          # Configuration Docker
├── src/                               # Code source principal
│   ├── client/                        # Application frontend (React + Vite)
│   │   ├── src/                       # Code source React
│   │   │   ├── App.jsx                # Composant principal
│   │   │   ├── main.jsx               # Point d'entrée
│   │   │   ├── assets/                # Ressources statiques
│   │   │   └── *.css                  # Styles CSS
│   │   ├── public/                    # Fichiers publics
│   │   ├── package.json               # Dépendances Node.js
│   │   ├── vite.config.js             # Configuration Vite
│   │   └── index.html                 # Template HTML
│   └── server/                        # Application backend (Spring Boot)
│       ├── src/                       # Code source Java
│       │   ├── main/                  # Code principal
│       │   │   ├── java/              # Classes Java
│       │   │   └── resources/         # Ressources de configuration
│       │   └── test/                  # Tests unitaires
│       ├── pom.xml                    # Configuration Maven
│       ├── mvnw                       # Maven Wrapper (Unix)
│       └── mvnw.cmd                   # Maven Wrapper (Windows)
├── scripts/                           # Scripts utilitaires
│   └── local-test.sh                  # Script de test local
├── deploy/                            # Configuration de déploiement
│   ├── deploy.sh                      # Script de déploiement
│   ├── nginx.conf                     # Configuration Nginx
│   └── server-setup.sh                # Configuration serveur
└── deliverables/                      # Livrables du projet
    └── week 1/                        # Livrables semaine 1
        ├── DescriptionProjet.md       # Description du projet
        ├── DescriptionChoixTechniques.md # Choix techniques
        ├── DescriptionProcTravail.md   # Processus de travail
        └── todo.txt                   # Liste des tâches
```

## 🛠️ Technology Stack

### Frontend

- **React 18** - Modern JavaScript library for building user interfaces
- **Vite** - Fast build tool and development server
- **CSS3** - Modern styling with gradients, animations, and responsive design
- **ES6+** - Modern JavaScript features

### Backend

- **Spring Boot 3.3.0** - Java framework for building production-ready applications
- **Maven** - Dependency management and build tool
- **Java 17** - Long-term support Java version
- **Firebase Authentication** - Real-time database for player login
- **Firebase Firestore** - Real-time database for game data

### DevOps & Deployment

- **Docker** - Containerization for consistent deployments
- **GitHub Actions** - CI/CD pipeline automation

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Java 17** - [Download here](https://adoptium.net/)
- **Node.js 18 or higher** - [Download here](https://nodejs.org/)
- **npm or yarn** - Comes with Node.js
- **Git** - [Download here](https://git-scm.com/)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd BloodOnTheClockTower-Assistant
```

### 2. Start the Backend Server

```bash
# Navigate to the server directory
cd src/server

# Run the Spring Boot application
./mvnw spring-boot:run

# Alternative: If you have Maven installed globally
mvn spring-boot:run
```

The backend server will start on `http://localhost:8080`

### 3. Start the Frontend Development Server

Open a new terminal window:

```bash
# Navigate to the client directory
cd src/client

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend application will start on `http://localhost:5173`

### 4. Access the Application

Open your web browser and navigate to `http://localhost:5173` to view the Blood on the Clocktower Assistant landing page.

## 🔧 Development

### Local Development Setup

1. **Clone and Install**:

   ```bash
   git clone <repository-url>
   cd BloodOnTheClockTower-Assistant

   # Install frontend dependencies
   cd src/client && npm install && cd ../..

   # Install backend dependencies (automatic with Maven)
   cd src/server && ./mvnw dependency:resolve && cd ../..
   ```

2. **Start Development Servers**:

   ```bash
   # Terminal 1: Start backend
   cd src/server && ./mvnw spring-boot:run

   # Terminal 2: Start frontend
   cd src/client && npm run dev
   ```

3. **Access Application**: Open `http://localhost:5173`

### Docker Development

```bash
# Build and test locally
./scripts/local-test.sh

# Or use Docker Compose
docker-compose up --build
```

### Backend Development

#### Running Tests

```bash
cd src/server
./mvnw test
```

#### Building for Production

```bash
cd src/server
./mvnw clean package
java -jar target/botc-assistant-0.0.1-SNAPSHOT.jar
```

#### Static Assets

The application serves static frontend assets through Spring Boot's resource handling. No database is required for the landing page.

### Frontend Development

#### Available Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

#### Environment Variables

The application automatically detects the environment:

- **Development**: Frontend served from `http://localhost:5173`
- **Production**: Static assets served from Spring Boot backend

### CI/CD Pipeline

The repository includes a complete CI/CD pipeline:

#### Workflow Triggers

- **Pull Requests**: Runs tests and security scans
- **Push to main**: Full deployment pipeline
- **Push to develop**: Tests only

#### Pipeline Stages

1. **Frontend Tests**: ESLint, build verification
2. **Backend Tests**: Unit tests, integration tests
3. **Security Scan**: Vulnerability detection
4. **Docker Build**: Multi-platform image creation
5. **Deploy**: Automated server deployment

#### Local Testing

```bash
# Test the complete pipeline locally
./scripts/local-test.sh

# Test individual components
cd src/client && npm test
cd src/server && ./mvnw test
```

## 🚀 Deployment

This application includes a complete CI/CD pipeline with Docker containerization and automated deployment.

### Quick Deployment (Recommended)

1. **Server Setup**: Run the automated server setup script on your Ubuntu/Debian server:

   ```bash
   curl -fsSL https://raw.githubusercontent.com/Bananonymous/BloodOnTheClockTower-Assistant/refs/heads/main/deploy/server-setup.sh -o server-setup.sh
   chmod +x server-setup.sh
   sudo ./server-setup.sh
   ```

2. **GitHub Configuration**: Add these secrets to your GitHub repository:

   - `DEPLOY_HOST`: Your server IP address
   - `DEPLOY_USER`: `botcassistant`
   - `DEPLOY_KEY`: Your private SSH key
   - `DEPLOY_PORT`: `22` (or your custom SSH port)

3. **Deploy**: Push to the main branch to trigger automatic deployment:
   ```bash
   git push origin main
   ```

### Manual Deployment

#### Docker Deployment (Single Container)

```bash
# Build the image
docker build -t botc-assistant .

# Run the container
docker run -d \
  --name botc-assistant \
  -p 8080:8080 \
  botc-assistant
```

#### Docker Compose Deployment

```bash
# Start all services
docker-compose up -d

# With Nginx proxy (production)
docker-compose --profile production up -d
```

#### Traditional Deployment

1. **Backend**: Build JAR with `./mvnw clean package` and run with Java 17+
2. **Frontend**: Build with `npm run build` and serve static files
3. **Database**: Ensure SQLite file permissions are correct

### CI/CD Pipeline Features

- ✅ **Automated Testing**: Frontend linting, backend unit tests, integration tests
- ✅ **Multi-stage Docker Build**: Optimized production container
- ✅ **GitHub Container Registry**: Automatic image building and pushing
- ✅ **Zero-downtime Deployment**: Health checks and automatic rollback
- ✅ **Security Scanning**: Vulnerability detection with Trivy
- ✅ **Database Backups**: Automatic backups before deployment

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 🐛 Troubleshooting

### Common Issues

#### Backend won't start

- Ensure Java 17+ is installed: `java -version`
- Check if port 8080 is available
- Verify Maven is working: `./mvnw -version`

#### Frontend won't start

- Ensure Node.js 18+ is installed: `node -version`
- Clear npm cache: `npm cache clean --force`
- Delete node_modules and reinstall: `rm -rf node_modules && npm install`

#### CORS Issues

- Ensure the backend CORS configuration includes your frontend URL
- Check browser console for specific CORS error messages

#### Static Asset Issues

- Clear browser cache if styles aren't loading
- Check that the build process completed successfully

## 📞 Support

If you encounter any issues or have questions, please:

1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information about the problem

---

**Built with ❤️ using Spring Boot and React**
