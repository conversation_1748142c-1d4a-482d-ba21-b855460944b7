package com.botc.assistant.roles.townsfolks;

/**
 * Ravenkeeper - Townsfolk role
 * 
 * If you die at night, you may choose a player: you learn their character.
 * 
 * The Ravenkeeper has a powerful death ability. If they die at night
 * (typically from the Demon), they may choose any player and learn
 * that player's exact character/role.
 */
public class Ravenkeeper extends TownsFolk {
    
    public static final String ROLE_ID = "ravenkeeper";
    public static final String ROLE_NAME = "Ravenkeeper";
    public static final String ROLE_DESCRIPTION = "If you die at night, you may choose a player: you learn their character.";
    
    private Ravenkeeper(Builder builder) {
        super(builder);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static Ravenkeeper create() {
        return builder().build();
    }
    
    /**
     * Builder for Ravenkeeper role
     */
    public static class Builder extends TownsFolk.Builder<Builder> {
        
        public Builder() {
            id(ROLE_ID);
            name(ROLE_NAME);
            description(ROLE_DESCRIPTION);
            hasNightAbility(false); // Death ability, not regular night ability
            hasDayAbility(false);
            setupInfo("If killed at night, may choose a player to learn their character");
        }
        
        @Override
        protected Builder self() {
            return this;
        }
        
        @Override
        public Ravenkeeper build() {
            return new Ravenkeeper(this);
        }
    }
}
